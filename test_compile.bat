@echo off
echo Testing POCO TCP Server Compilation
echo ===================================

:: Check for Visual Studio
where cl >nul 2>&1
if errorlevel 1 (
    echo Error: Visual Studio compiler not found
    echo Please run this script from Visual Studio Developer Command Prompt
    exit /b 1
)

:: Check for CMake
where cmake >nul 2>&1
if errorlevel 1 (
    echo Error: CMake not found
    echo Please install CMake and add it to PATH
    exit /b 1
)

:: Test compilation with minimal setup
echo Testing basic compilation...

:: Create a minimal test
echo #include "protocol.h" > test_protocol.cpp
echo int main() { return 0; } >> test_protocol.cpp

cl /EHsc test_protocol.cpp /Fe:test_protocol.exe
if errorlevel 1 (
    echo Error: Protocol header compilation failed
    del test_protocol.cpp
    exit /b 1
) else (
    echo Protocol header compilation: OK
    del test_protocol.cpp test_protocol.exe
)

:: Test C client API signatures
echo Testing C client API signatures...
cl test_client_compile.c /Fe:test_api.exe
if errorlevel 1 (
    echo Error: C client API signature test failed
    exit /b 1
) else (
    echo C client API signatures: OK
    test_api.exe
    del test_api.exe
)

:: Test buffer size calculations
echo Testing buffer size calculations...
cl test_buffer_fix.c /Fe:test_buffer.exe
if errorlevel 1 (
    echo Error: Buffer test compilation failed
    exit /b 1
) else (
    echo Buffer size test: OK
    test_buffer.exe
    del test_buffer.exe
)

:: Test C client compilation
echo Testing C client compilation...
cl client_c.c ws2_32.lib /Fe:test_client_c.exe
if errorlevel 1 (
    echo Error: C client compilation failed
    exit /b 1
) else (
    echo C client compilation: OK
    del test_client_c.exe
)

:: Test debug client compilation
echo Testing debug client compilation...
cl debug_client.c ws2_32.lib /Fe:test_debug_client.exe
if errorlevel 1 (
    echo Error: Debug client compilation failed
    exit /b 1
) else (
    echo Debug client compilation: OK
    del test_debug_client.exe
)

:: Test Python client syntax
echo Testing Python client syntax...
python -m py_compile client_python_simple.py
if errorlevel 1 (
    echo Error: Python client syntax error
    exit /b 1
) else (
    echo Python client syntax: OK
    del client_python_simple.pyc 2>nul
)

echo.
echo Basic compilation test passed!
echo.
echo To build the full server:
echo 1. Install POCO C++: vcpkg install poco[net,util]:x64-windows
echo 2. Download MemProcFS and extract to C:\MemProcFS
echo 3. Run: build_poco.bat
echo.
