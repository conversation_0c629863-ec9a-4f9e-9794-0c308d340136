.版本 2

.程序集 MemProcFS共享内存客户端

.常量 MEMPROCFS_SHM_NAME, "MemProcFS_SharedMemory"
.常量 MEMPROCFS_SHM_SIZE, 1048576
.常量 MEMPROCFS_MUTEX_NAME, "MemProcFS_Mutex"
.常量 MEMPROCFS_REQUEST_EVENT, "MemProcFS_Request"
.常量 MEMPROCFS_RESPONSE_EVENT, "MemProcFS_Response"

.常量 PACKET_MAGIC, 1297240902
.常量 MEMPROCFS_PROTOCOL_VERSION, 2

.常量 CMD_INIT, 0
.常量 CMD_GET_PROCESS_ID, 1
.常量 CMD_GET_MODULE_BASE, 2
.常量 CMD_READ_MEMORY, 3
.常量 CMD_WRITE_MEMORY, 4

.常量 STATUS_SUCCESS, 0
.常量 STATUS_ERROR, 1
.常量 STATUS_NOT_FOUND, 2

.常量 FILE_MAP_ALL_ACCESS, 31
.常量 MUTEX_ALL_ACCESS, 2031617
.常量 EVENT_ALL_ACCESS, 2031619
.常量 WAIT_OBJECT_0, 0
.常量 INFINITE, -1

.DLL命令 OpenFileMappingA, 整数型, "kernel32", "OpenFileMappingA"
    .参数 dwDesiredAccess, 整数型
    .参数 bInheritHandle, 逻辑型
    .参数 lpName, 文本型

.DLL命令 MapViewOfFile, 整数型, "kernel32", "MapViewOfFile"
    .参数 hFileMappingObject, 整数型
    .参数 dwDesiredAccess, 整数型
    .参数 dwFileOffsetHigh, 整数型
    .参数 dwFileOffsetLow, 整数型
    .参数 dwNumberOfBytesToMap, 整数型

.DLL命令 UnmapViewOfFile, 逻辑型, "kernel32", "UnmapViewOfFile"
    .参数 lpBaseAddress, 整数型

.DLL命令 OpenMutexA, 整数型, "kernel32", "OpenMutexA"
    .参数 dwDesiredAccess, 整数型
    .参数 bInheritHandle, 逻辑型
    .参数 lpName, 文本型

.DLL命令 OpenEventA, 整数型, "kernel32", "OpenEventA"
    .参数 dwDesiredAccess, 整数型
    .参数 bInheritHandle, 逻辑型
    .参数 lpName, 文本型

.DLL命令 WaitForSingleObject, 整数型, "kernel32", "WaitForSingleObject"
    .参数 hHandle, 整数型
    .参数 dwMilliseconds, 整数型

.DLL命令 ReleaseMutex, 逻辑型, "kernel32", "ReleaseMutex"
    .参数 hMutex, 整数型

.DLL命令 SetEvent, 逻辑型, "kernel32", "SetEvent"
    .参数 hEvent, 整数型

.DLL命令 CloseHandle, 逻辑型, "kernel32", "CloseHandle"
    .参数 hObject, 整数型

.DLL命令 RtlMoveMemory, , "kernel32", "RtlMoveMemory"
    .参数 Destination, 整数型
    .参数 Source, 整数型
    .参数 Length, 整数型

.DLL命令 RtlZeroMemory, , "kernel32", "RtlZeroMemory"
    .参数 Destination, 整数型
    .参数 Length, 整数型

.变量 h共享内存, 整数型
.变量 h互斥锁, 整数型
.变量 h请求事件, 整数型
.变量 h响应事件, 整数型
.变量 p共享内存, 整数型
.变量 序列号, 整数型

.子程序 初始化客户端, 逻辑型, 公开
.参数 VMware进程ID, 整数型

序列号 ＝ 1

' 打开共享内存
h共享内存 ＝ OpenFileMappingA (FILE_MAP_ALL_ACCESS, 假, MEMPROCFS_SHM_NAME)
.如果 (h共享内存 ＝ 0)
    输出调试文本 ("无法打开共享内存，服务器是否运行？")
    返回 (假)
.如果结束

' 映射共享内存
p共享内存 ＝ MapViewOfFile (h共享内存, FILE_MAP_ALL_ACCESS, 0, 0, MEMPROCFS_SHM_SIZE)
.如果 (p共享内存 ＝ 0)
    输出调试文本 ("无法映射共享内存")
    CloseHandle (h共享内存)
    返回 (假)
.如果结束

' 打开同步对象
h互斥锁 ＝ OpenMutexA (MUTEX_ALL_ACCESS, 假, MEMPROCFS_MUTEX_NAME)
h请求事件 ＝ OpenEventA (EVENT_ALL_ACCESS, 假, MEMPROCFS_REQUEST_EVENT)
h响应事件 ＝ OpenEventA (EVENT_ALL_ACCESS, 假, MEMPROCFS_RESPONSE_EVENT)

.如果 (h互斥锁 ＝ 0 或者 h请求事件 ＝ 0 或者 h响应事件 ＝ 0)
    输出调试文本 ("无法打开同步对象")
    清理客户端 ()
    返回 (假)
.如果结束

' 发送初始化请求
WaitForSingleObject (h互斥锁, INFINITE)

' 构造请求头 (16字节)
写入整数 (p共享内存, PACKET_MAGIC)
写入字节 (p共享内存 ＋ 4, MEMPROCFS_PROTOCOL_VERSION)
写入字节 (p共享内存 ＋ 5, CMD_INIT)
写入短整数 (p共享内存 ＋ 6, 0)  ' reserved
写入整数 (p共享内存 ＋ 8, 4)   ' data_size
写入整数 (p共享内存 ＋ 12, 序列号)
序列号 ＝ 序列号 ＋ 1

' 写入VMware进程ID
写入整数 (p共享内存 ＋ 16, VMware进程ID)

SetEvent (h请求事件)
ReleaseMutex (h互斥锁)

' 等待响应
.如果 (WaitForSingleObject (h响应事件, 5000) ≠ WAIT_OBJECT_0)
    输出调试文本 ("等待初始化响应超时")
    清理客户端 ()
    返回 (假)
.如果结束

' 检查响应状态
.变量 响应状态, 字节型
响应状态 ＝ 读字节 (p共享内存 ＋ 524288 ＋ 4)  ' 响应区域 + magic(4) + status(1)

.如果 (响应状态 ≠ STATUS_SUCCESS)
    输出调试文本 ("初始化VMware进程失败: " ＋ 到文本 (VMware进程ID))
    清理客户端 ()
    返回 (假)
.如果结束

输出调试文本 ("成功初始化VMware进程: " ＋ 到文本 (VMware进程ID))
返回 (真)

.子程序 清理客户端, , 公开

.如果 (h响应事件 ≠ 0)
    CloseHandle (h响应事件)
    h响应事件 ＝ 0
.如果结束

.如果 (h请求事件 ≠ 0)
    CloseHandle (h请求事件)
    h请求事件 ＝ 0
.如果结束

.如果 (h互斥锁 ≠ 0)
    CloseHandle (h互斥锁)
    h互斥锁 ＝ 0
.如果结束

.如果 (p共享内存 ≠ 0)
    UnmapViewOfFile (p共享内存)
    p共享内存 ＝ 0
.如果结束

.如果 (h共享内存 ≠ 0)
    CloseHandle (h共享内存)
    h共享内存 ＝ 0
.如果结束

.子程序 获取进程ID, 整数型, 公开
.参数 进程名称, 文本型

.如果 (p共享内存 ＝ 0)
    返回 (0)
.如果结束

WaitForSingleObject (h互斥锁, INFINITE)

' 构造请求头
写入整数 (p共享内存, PACKET_MAGIC)
写入字节 (p共享内存 ＋ 4, MEMPROCFS_PROTOCOL_VERSION)
写入字节 (p共享内存 ＋ 5, CMD_GET_PROCESS_ID)
写入短整数 (p共享内存 ＋ 6, 0)
写入整数 (p共享内存 ＋ 8, 64)
写入整数 (p共享内存 ＋ 12, 序列号)
序列号 ＝ 序列号 ＋ 1

' 写入进程名称 (64字节)
.变量 进程名字节, 字节集
进程名字节 ＝ 到字节集 (进程名称)
.如果 (取字节集长度 (进程名字节) ＞ 63)
    进程名字节 ＝ 取字节集左边 (进程名字节, 63)
.如果结束
RtlZeroMemory (p共享内存 ＋ 16, 64)
RtlMoveMemory (p共享内存 ＋ 16, 取字节集数据地址 (进程名字节), 取字节集长度 (进程名字节))

SetEvent (h请求事件)
ReleaseMutex (h互斥锁)

' 等待响应
.如果 (WaitForSingleObject (h响应事件, 5000) ≠ WAIT_OBJECT_0)
    输出调试文本 ("等待获取进程ID响应超时")
    返回 (0)
.如果结束

' 检查响应状态
.变量 响应状态, 字节型
响应状态 ＝ 读字节 (p共享内存 ＋ 524288 ＋ 4)

.如果 (响应状态 ≠ STATUS_SUCCESS)
    返回 (0)
.如果结束

' 读取进程ID
.变量 进程ID, 整数型
进程ID ＝ 读整数 (p共享内存 ＋ 524288 ＋ 12)  ' 响应区域 + 响应头(12) + 进程ID(4)
返回 (进程ID)

.子程序 读取内存, 字节集, 公开
.参数 进程ID, 整数型
.参数 地址, 长整数型
.参数 大小, 整数型

.如果 (p共享内存 ＝ 0 或者 大小 ＝ 0)
    返回 ({ })
.如果结束

WaitForSingleObject (h互斥锁, INFINITE)

' 构造请求头
写入整数 (p共享内存, PACKET_MAGIC)
写入字节 (p共享内存 ＋ 4, MEMPROCFS_PROTOCOL_VERSION)
写入字节 (p共享内存 ＋ 5, CMD_READ_MEMORY)
写入短整数 (p共享内存 ＋ 6, 0)
写入整数 (p共享内存 ＋ 8, 16)  ' sizeof(memory_request_t)
写入整数 (p共享内存 ＋ 12, 序列号)
序列号 ＝ 序列号 ＋ 1

' 写入内存请求数据
写入整数 (p共享内存 ＋ 16, 进程ID)
写入长整数 (p共享内存 ＋ 20, 地址)
写入整数 (p共享内存 ＋ 28, 大小)

SetEvent (h请求事件)
ReleaseMutex (h互斥锁)

' 等待响应
.如果 (WaitForSingleObject (h响应事件, 5000) ≠ WAIT_OBJECT_0)
    输出调试文本 ("等待读取内存响应超时")
    返回 ({ })
.如果结束

' 检查响应状态
.变量 响应状态, 字节型
响应状态 ＝ 读字节 (p共享内存 ＋ 524288 ＋ 4)

.如果 (响应状态 ≠ STATUS_SUCCESS)
    返回 ({ })
.如果结束

' 读取处理的字节数
.变量 处理字节数, 整数型
处理字节数 ＝ 读整数 (p共享内存 ＋ 524288 ＋ 12)

' 读取实际数据
.变量 数据, 字节集
数据 ＝ 取字节集 (p共享内存 ＋ 524288 ＋ 16, 处理字节数)
返回 (数据)

.子程序 示例程序, , 公开

输出调试文本 ("MemProcFS共享内存客户端示例")
输出调试文本 ("==============================")

' 初始化客户端
.变量 VMware进程ID, 整数型
VMware进程ID ＝ 12345

输出调试文本 ("正在初始化VMware进程ID: " ＋ 到文本 (VMware进程ID))

.如果 (初始化客户端 (VMware进程ID) ＝ 假)
    输出调试文本 ("客户端初始化失败")
    返回 ()
.如果结束

' 获取进程ID
输出调试文本 ("正在查找notepad.exe...")
.变量 进程ID, 整数型
进程ID ＝ 获取进程ID ("notepad.exe")

.如果 (进程ID ＞ 0)
    输出调试文本 ("找到notepad.exe，进程ID: " ＋ 到文本 (进程ID))
    
    ' 这里可以添加更多操作，比如获取模块基址、读取内存等
    
.否则
    输出调试文本 ("未找到notepad.exe (确保它在虚拟机中运行)")
.如果结束

' 清理
清理客户端 ()
输出调试文本 ("客户端示例完成")

' 辅助函数
.子程序 写入整数, , 
.参数 地址, 整数型
.参数 值, 整数型
RtlMoveMemory (地址, 取变量地址 (值), 4)

.子程序 写入字节, , 
.参数 地址, 整数型
.参数 值, 字节型
RtlMoveMemory (地址, 取变量地址 (值), 1)

.子程序 写入短整数, , 
.参数 地址, 整数型
.参数 值, 短整数型
RtlMoveMemory (地址, 取变量地址 (值), 2)

.子程序 写入长整数, , 
.参数 地址, 整数型
.参数 值, 长整数型
RtlMoveMemory (地址, 取变量地址 (值), 8)

.子程序 读字节, 字节型, 
.参数 地址, 整数型
.变量 值, 字节型
RtlMoveMemory (取变量地址 (值), 地址, 1)
返回 (值)

.子程序 读整数, 整数型, 
.参数 地址, 整数型
.变量 值, 整数型
RtlMoveMemory (取变量地址 (值), 地址, 4)
返回 (值)

.子程序 取字节集, 字节集, 
.参数 地址, 整数型
.参数 长度, 整数型
.变量 数据, 字节集
重定义数组 (数据, 假, 长度)
RtlMoveMemory (取字节集数据地址 (数据), 地址, 长度)
返回 (数据)
