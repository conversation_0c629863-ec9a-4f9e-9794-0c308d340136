/*
 * 测试C客户端编译
 * 验证所有函数签名是否正确
 */

#include <stdio.h>
#include "protocol.h"

// 模拟客户端结构
typedef struct {
    int socket;
    uint32_t sequence;
} memprocfs_client_t;

// 声明简化API函数
int memprocfs_client_init(memprocfs_client_t* client, const char* server_ip, uint16_t port, uint32_t vmware_process_id);
void memprocfs_client_cleanup(memprocfs_client_t* client);
int memprocfs_read_memory_simple(memprocfs_client_t* client, uint32_t process_id, uint64_t address, void* buffer, uint32_t size);
int memprocfs_write_memory_simple(memprocfs_client_t* client, uint32_t process_id, uint64_t address, const void* buffer, uint32_t size);
uint32_t memprocfs_get_process_id(memprocfs_client_t* client, const char* process_name);
uint64_t memprocfs_get_module_base(memprocfs_client_t* client, uint32_t process_id, const char* module_name);

int main() {
    printf("测试C客户端API函数签名\n");
    
    memprocfs_client_t client;
    
    // 测试初始化函数签名
    printf("✓ memprocfs_client_init(client, ip, port, vmware_pid)\n");
    
    // 测试简化API函数签名
    printf("✓ memprocfs_read_memory_simple(client, pid, addr, buf, size) -> int\n");
    printf("✓ memprocfs_write_memory_simple(client, pid, addr, buf, size) -> int\n");
    printf("✓ memprocfs_get_process_id(client, name) -> uint32_t\n");
    printf("✓ memprocfs_get_module_base(client, pid, module) -> uint64_t\n");
    
    printf("\n所有函数签名验证通过！\n");
    
    return 0;
}
