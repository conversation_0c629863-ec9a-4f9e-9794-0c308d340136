#pragma once

/*
 * MemProcFS Shared Memory Protocol Definition
 *
 * 简化的共享内存通信协议，用于32位客户端与64位MemProcFS服务器通信
 *
 * Protocol Features:
 * - 共享内存通信，高效快速
 * - 简化的数据头和响应头
 * - 服务端处理复杂逻辑
 * - 客户端操作简单
 * - Windows专用
 *
 * Version: 2.0
 * Target: Windows only
 */

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 共享内存配置
#define MEMPROCFS_SHM_NAME      "MemProcFS_SharedMemory"
#define MEMPROCFS_SHM_SIZE      (1024 * 1024)  // 1MB
#define MEMPROCFS_MUTEX_NAME    "MemProcFS_Mutex"
#define MEMPROCFS_REQUEST_EVENT "MemProcFS_Request"
#define MEMPROCFS_RESPONSE_EVENT "MemProcFS_Response"

// Protocol version
#define MEMPROCFS_PROTOCOL_VERSION  2

// Magic value
#define PACKET_MAGIC            0x4D505346  // "MPSF" (MemProcFS Shared)

// 简化的命令类型
typedef enum {
    CMD_INIT               = 0x00,  // 初始化VMware进程
    CMD_GET_PROCESS_ID     = 0x01,  // 根据名称获取进程ID
    CMD_GET_MODULE_BASE    = 0x02,  // 获取模块基址
    CMD_READ_MEMORY        = 0x03,  // 读取内存
    CMD_WRITE_MEMORY       = 0x04,  // 写入内存
    CMD_SHUTDOWN           = 0xFF   // 关闭服务器
} command_t;

// 状态码
typedef enum {
    STATUS_SUCCESS         = 0,
    STATUS_ERROR           = 1,
    STATUS_NOT_FOUND       = 2,
    STATUS_ACCESS_DENIED   = 3,
    STATUS_INVALID_PARAM   = 4,
    STATUS_NOT_INITIALIZED = 5
} status_t;

// 简化的请求头 (16字节)
#pragma pack(push, 1)
typedef struct {
    uint32_t magic;         // Magic number
    uint8_t  version;       // Protocol version
    uint8_t  command;       // Command type
    uint16_t reserved;      // Reserved for future use
    uint32_t data_size;     // Size of following data
    uint32_t sequence;      // Sequence number
} request_header_t;

// 简化的响应头 (12字节)
typedef struct {
    uint32_t magic;         // Magic number
    uint8_t  status;        // Status code
    uint8_t  reserved[3];   // Reserved
    uint32_t data_size;     // Size of following data
} response_header_t;

// 初始化请求
typedef struct {
    uint32_t vmware_process_id;
} init_request_t;

// 获取进程ID请求
typedef struct {
    char process_name[64];
} get_process_id_request_t;

// 获取进程ID响应
typedef struct {
    uint32_t process_id;
} get_process_id_response_t;

// 获取模块基址请求
typedef struct {
    uint32_t process_id;
    char module_name[64];
} get_module_base_request_t;

// 获取模块基址响应
typedef struct {
    uint64_t base_address;
} get_module_base_response_t;

// 内存操作请求
typedef struct {
    uint32_t process_id;
    uint64_t address;
    uint32_t size;
    // 对于写操作，数据跟在这个结构后面
} memory_request_t;

// 内存操作响应
typedef struct {
    uint32_t bytes_processed;
    // 对于读操作，数据跟在这个结构后面
} memory_response_t;

// 共享内存布局
typedef struct {
    // 请求区域 (前512KB)
    request_header_t request_header;
    uint8_t request_data[512 * 1024 - sizeof(request_header_t)];

    // 响应区域 (后512KB)
    response_header_t response_header;
    uint8_t response_data[512 * 1024 - sizeof(response_header_t)];
} shared_memory_t;
#pragma pack(pop)

// 辅助函数声明和实现
#ifdef __cplusplus
inline void init_request_header(request_header_t* header, command_t cmd, uint32_t data_size, uint32_t sequence) {
    header->magic = PACKET_MAGIC;
    header->version = MEMPROCFS_PROTOCOL_VERSION;
    header->command = (uint8_t)cmd;
    header->reserved = 0;
    header->data_size = data_size;
    header->sequence = sequence;
}

inline void init_response_header(response_header_t* header, status_t status, uint32_t data_size) {
    header->magic = PACKET_MAGIC;
    header->status = (uint8_t)status;
    header->reserved[0] = 0;
    header->reserved[1] = 0;
    header->reserved[2] = 0;
    header->data_size = data_size;
}
#else
// C语言版本的辅助函数
static void init_request_header(request_header_t* header, command_t cmd, uint32_t data_size, uint32_t sequence) {
    header->magic = PACKET_MAGIC;
    header->version = MEMPROCFS_PROTOCOL_VERSION;
    header->command = (uint8_t)cmd;
    header->reserved = 0;
    header->data_size = data_size;
    header->sequence = sequence;
}

static void init_response_header(response_header_t* header, status_t status, uint32_t data_size) {
    header->magic = PACKET_MAGIC;
    header->status = (uint8_t)status;
    header->reserved[0] = 0;
    header->reserved[1] = 0;
    header->reserved[2] = 0;
    header->data_size = data_size;
}
#endif

#ifdef __cplusplus
}
#endif
