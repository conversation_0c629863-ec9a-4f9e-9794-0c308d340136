#pragma once

/*
 * MemProcFS TCP Server Protocol Definition
 *
 * Simple and efficient binary protocol for communication between
 * 32-bit clients (any language) and 64-bit MemProcFS TCP server.
 *
 * Protocol Features:
 * - Binary format for efficiency
 * - Fixed-size headers for easy parsing
 * - Support for memory read/write operations
 * - Process and module management
 * - Error handling and status codes
 * - TCP network communication
 *
 * Version: 1.0
 * Target: Windows 10 only
 */

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// Protocol version
#define MEMPROCFS_PROTOCOL_VERSION  1
#define PROTOCOL_VERSION            MEMPROCFS_PROTOCOL_VERSION  // Alias for compatibility

// Default server settings
#define MEMPROCFS_DEFAULT_PORT      12345
#define MEMPROCFS_MAX_PACKET_SIZE   (1024 * 1024)  // 1MB max packet
#define MEMPROCFS_MAX_PROCESS_NAME  260
#define MEMPROCFS_MAX_MODULE_NAME   260

// Command types
typedef enum {
    // Connection management
    CMD_INIT               = 0x00,  // Initialize with VMware process ID
    CMD_PING               = 0x01,
    CMD_GET_VERSION        = 0x02,

    // Memory operations
    CMD_READ_MEMORY        = 0x10,
    CMD_WRITE_MEMORY       = 0x11,
    CMD_READ_PHYSICAL      = 0x12,
    CMD_WRITE_PHYSICAL     = 0x13,

    // Process management
    CMD_GET_PROCESS_LIST   = 0x20,
    CMD_GET_PROCESS_BY_NAME = 0x21,
    CMD_GET_PROCESS_INFO   = 0x22,

    // Module management
    CMD_GET_MODULE_BASE    = 0x30,
    CMD_GET_MODULE_LIST    = 0x31,
    CMD_GET_MODULE_INFO    = 0x32,

    // Response
    CMD_RESPONSE           = 0x80,
    CMD_ERROR              = 0xFF
} command_type_t;

// Status codes
typedef enum {
    STATUS_SUCCESS              = 0x00,
    STATUS_ERROR_INVALID_CMD    = 0x01,
    STATUS_ERROR_INVALID_PARAM  = 0x02,
    STATUS_ERROR_READ_FAILED    = 0x03,
    STATUS_ERROR_WRITE_FAILED   = 0x04,
    STATUS_ERROR_PROCESS_NOT_FOUND = 0x05,
    STATUS_ERROR_MODULE_NOT_FOUND  = 0x06,
    STATUS_ERROR_MEMORY_ACCESS  = 0x07,
    STATUS_ERROR_INTERNAL       = 0x08,
    STATUS_ERROR_NOT_INITIALIZED = 0x09,
    STATUS_ERROR_BUFFER_TOO_SMALL = 0x0A
} status_code_t;

// Packet header (fixed size: 16 bytes)
#pragma pack(push, 1)
typedef struct {
    uint32_t magic;         // Magic number: 0x4D505246 ("MPRF")
    uint8_t  version;       // Protocol version
    uint8_t  command;       // Command type
    uint16_t flags;         // Flags (reserved)
    uint32_t data_size;     // Size of data following header
    uint32_t sequence;      // Sequence number for request/response matching
} packet_header_t;
#pragma pack(pop)

#define PACKET_MAGIC 0x4D505246  // "MPRF"

// Memory operation structures
#pragma pack(push, 1)
typedef struct {
    uint32_t process_id;    // Process ID (0xFFFFFFFF for physical memory)
    uint64_t address;       // Memory address
    uint32_t size;          // Number of bytes to read/write
} memory_request_t;

typedef struct {
    uint32_t bytes_processed; // Actual bytes read/written
    // Data follows this structure
} memory_response_t;
#pragma pack(pop)

// Process management structures
#pragma pack(push, 1)
typedef struct {
    char process_name[MEMPROCFS_MAX_PROCESS_NAME];
} process_by_name_request_t;

typedef struct {
    uint32_t process_id;
} process_by_name_response_t;

typedef struct {
    uint32_t process_id;
    uint32_t parent_process_id;
    char process_name[MEMPROCFS_MAX_PROCESS_NAME];
    uint64_t eprocess_address;
    uint64_t peb_address;
    uint32_t session_id;
    uint32_t is_wow64;
    uint64_t create_time;
} process_info_t;

typedef struct {
    uint32_t count;
    // process_info_t entries follow
} process_list_response_t;
#pragma pack(pop)

// Module management structures
#pragma pack(push, 1)
typedef struct {
    uint32_t process_id;
    char module_name[MEMPROCFS_MAX_MODULE_NAME];
} module_request_t;

typedef struct {
    uint64_t base_address;
} module_base_response_t;

typedef struct {
    uint64_t base_address;
    uint64_t entry_point;
    uint32_t image_size;
    uint32_t checksum;
    uint32_t timestamp;
    uint32_t is_wow64;
    char module_name[MEMPROCFS_MAX_MODULE_NAME];
    char module_path[260];
} module_info_t;

typedef struct {
    uint32_t count;
    // module_info_t entries follow
} module_list_response_t;
#pragma pack(pop)

// Response header
#pragma pack(push, 1)
typedef struct {
    uint8_t status;         // Status code
    uint8_t reserved[3];    // Reserved for alignment
    uint32_t data_size;     // Size of response data
    // Response data follows
} response_header_t;
#pragma pack(pop)

// Helper functions for packet creation and parsing
static inline void init_packet_header(packet_header_t* header, uint8_t command, uint32_t data_size, uint32_t sequence) {
    header->magic = PACKET_MAGIC;
    header->version = MEMPROCFS_PROTOCOL_VERSION;
    header->command = command;
    header->flags = 0;
    header->data_size = data_size;
    header->sequence = sequence;
}

static inline int validate_packet_header(const packet_header_t* header) {
    return (header->magic == PACKET_MAGIC && 
            header->version == MEMPROCFS_PROTOCOL_VERSION &&
            header->data_size <= MEMPROCFS_MAX_PACKET_SIZE);
}

// Protocol usage examples:
/*
1. Read Memory:
   Client -> Server: 
     Header: CMD_READ_MEMORY, data_size=16, sequence=1
     Data: memory_request_t{process_id=1234, address=0x400000, size=256}
   
   Server -> Client:
     Header: CMD_RESPONSE, data_size=260, sequence=1
     Data: response_header_t{status=STATUS_SUCCESS, data_size=256} + 256 bytes of memory

2. Get Process by Name:
   Client -> Server:
     Header: CMD_GET_PROCESS_BY_NAME, data_size=260, sequence=2
     Data: process_by_name_request_t{process_name="notepad.exe"}
   
   Server -> Client:
     Header: CMD_RESPONSE, data_size=8, sequence=2
     Data: response_header_t{status=STATUS_SUCCESS, data_size=4} + process_by_name_response_t{process_id=5678}

3. Get Module Base:
   Client -> Server:
     Header: CMD_GET_MODULE_BASE, data_size=264, sequence=3
     Data: module_request_t{process_id=5678, module_name="ntdll.dll"}
   
   Server -> Client:
     Header: CMD_RESPONSE, data_size=12, sequence=3
     Data: response_header_t{status=STATUS_SUCCESS, data_size=8} + module_base_response_t{base_address=0x77000000}
*/

#ifdef __cplusplus
}
#endif
