/*
 * MemProcFS Shared Memory Server
 *
 * 使用共享内存通信的MemProcFS服务器，支持多VMware进程
 * 简化的协议和客户端操作
 */

#define _CRT_SECURE_NO_WARNINGS
#include <iostream>
#include <string>
#include <thread>
#include <atomic>
#include <chrono>
#include <mutex>
#include <vector>
#include <map>
#include <memory>

// Windows includes
#include <windows.h>
#include <process.h>

// MemProcFS includes
#include "vmmdll.h"

// 共享内存协议
#include "shared_memory_protocol.h"

// 服务器类
class MemProcFSServer {
private:
    // 共享内存句柄
    HANDLE m_hSharedMemory;
    HANDLE m_hMutex;
    HANDLE m_hRequestEvent;
    HANDLE m_hResponseEvent;
    
    // 共享内存映射
    shared_memory_t* m_pSharedMemory;
    
    // 服务器状态
    std::atomic<bool> m_running;
    
    // VMware进程ID和VMM句柄映射
    std::map<uint32_t, VMM_HANDLE> m_vmmHandles;
    std::mutex m_vmmMutex;
    
    // 处理请求的线程
    std::thread m_serverThread;
    
public:
    MemProcFSServer() 
        : m_hSharedMemory(NULL), m_hMutex(NULL), 
          m_hRequestEvent(NULL), m_hResponseEvent(NULL),
          m_pSharedMemory(NULL), m_running(false) {
    }
    
    ~MemProcFSServer() {
        Stop();
    }
    
    // 初始化服务器
    bool Initialize() {
        // 创建共享内存
        m_hSharedMemory = CreateFileMappingA(
            INVALID_HANDLE_VALUE,
            NULL,
            PAGE_READWRITE,
            0,
            MEMPROCFS_SHM_SIZE,
            MEMPROCFS_SHM_NAME
        );
        
        if (!m_hSharedMemory) {
            std::cerr << "Failed to create shared memory: " << GetLastError() << std::endl;
            return false;
        }
        
        // 映射共享内存
        m_pSharedMemory = (shared_memory_t*)MapViewOfFile(
            m_hSharedMemory,
            FILE_MAP_ALL_ACCESS,
            0,
            0,
            MEMPROCFS_SHM_SIZE
        );
        
        if (!m_pSharedMemory) {
            std::cerr << "Failed to map shared memory: " << GetLastError() << std::endl;
            CloseHandle(m_hSharedMemory);
            return false;
        }
        
        // 创建互斥锁
        m_hMutex = CreateMutexA(NULL, FALSE, MEMPROCFS_MUTEX_NAME);
        if (!m_hMutex) {
            std::cerr << "Failed to create mutex: " << GetLastError() << std::endl;
            UnmapViewOfFile(m_pSharedMemory);
            CloseHandle(m_hSharedMemory);
            return false;
        }
        
        // 创建事件
        m_hRequestEvent = CreateEventA(NULL, FALSE, FALSE, MEMPROCFS_REQUEST_EVENT);
        if (!m_hRequestEvent) {
            std::cerr << "Failed to create request event: " << GetLastError() << std::endl;
            CloseHandle(m_hMutex);
            UnmapViewOfFile(m_pSharedMemory);
            CloseHandle(m_hSharedMemory);
            return false;
        }
        
        m_hResponseEvent = CreateEventA(NULL, FALSE, FALSE, MEMPROCFS_RESPONSE_EVENT);
        if (!m_hResponseEvent) {
            std::cerr << "Failed to create response event: " << GetLastError() << std::endl;
            CloseHandle(m_hRequestEvent);
            CloseHandle(m_hMutex);
            UnmapViewOfFile(m_pSharedMemory);
            CloseHandle(m_hSharedMemory);
            return false;
        }
        
        // 初始化共享内存
        ZeroMemory(m_pSharedMemory, MEMPROCFS_SHM_SIZE);
        
        return true;
    }
    
    // 启动服务器
    void Start() {
        if (!m_pSharedMemory) {
            std::cerr << "Server not initialized" << std::endl;
            return;
        }
        
        m_running = true;
        
        // 创建服务器线程
        m_serverThread = std::thread(&MemProcFSServer::ServerThread, this);
        
        std::cout << "MemProcFS Shared Memory Server v2.0" << std::endl;
        std::cout << "Shared Memory: " << MEMPROCFS_SHM_NAME << std::endl;
        std::cout << "Waiting for client requests..." << std::endl;
        std::cout << "Press Ctrl+C to stop the server" << std::endl;
    }
    
    // 停止服务器
    void Stop() {
        m_running = false;
        
        // 等待服务器线程结束
        if (m_serverThread.joinable()) {
            m_serverThread.join();
        }
        
        // 关闭所有VMM句柄
        {
            std::lock_guard<std::mutex> lock(m_vmmMutex);
            for (auto& pair : m_vmmHandles) {
                if (pair.second) {
                    VMMDLL_Close(pair.second);
                }
            }
            m_vmmHandles.clear();
        }
        
        // 清理资源
        if (m_hResponseEvent) {
            CloseHandle(m_hResponseEvent);
            m_hResponseEvent = NULL;
        }
        
        if (m_hRequestEvent) {
            CloseHandle(m_hRequestEvent);
            m_hRequestEvent = NULL;
        }
        
        if (m_hMutex) {
            CloseHandle(m_hMutex);
            m_hMutex = NULL;
        }
        
        if (m_pSharedMemory) {
            UnmapViewOfFile(m_pSharedMemory);
            m_pSharedMemory = NULL;
        }
        
        if (m_hSharedMemory) {
            CloseHandle(m_hSharedMemory);
            m_hSharedMemory = NULL;
        }
        
        std::cout << "Server stopped" << std::endl;
    }
    
private:
    // 服务器线程
    void ServerThread() {
        while (m_running) {
            // 等待请求事件
            DWORD waitResult = WaitForSingleObject(m_hRequestEvent, 100);
            
            if (waitResult == WAIT_OBJECT_0) {
                // 获取互斥锁
                WaitForSingleObject(m_hMutex, INFINITE);
                
                // 处理请求
                ProcessRequest();
                
                // 释放互斥锁
                ReleaseMutex(m_hMutex);
            }
        }
    }
    
    // 处理请求
    void ProcessRequest() {
        // 验证请求头
        if (m_pSharedMemory->request_header.magic != PACKET_MAGIC) {
            std::cerr << "Invalid request magic: 0x" << std::hex << m_pSharedMemory->request_header.magic << std::dec << std::endl;
            SendErrorResponse(STATUS_INVALID_PARAM);
            return;
        }
        
        if (m_pSharedMemory->request_header.version != MEMPROCFS_PROTOCOL_VERSION) {
            std::cerr << "Invalid protocol version: " << (int)m_pSharedMemory->request_header.version << std::endl;
            SendErrorResponse(STATUS_INVALID_PARAM);
            return;
        }
        
        // 根据命令类型处理请求
        switch (m_pSharedMemory->request_header.command) {
            case CMD_INIT:
                HandleInit();
                break;
                
            case CMD_GET_PROCESS_ID:
                HandleGetProcessId();
                break;
                
            case CMD_GET_MODULE_BASE:
                HandleGetModuleBase();
                break;
                
            case CMD_READ_MEMORY:
                HandleReadMemory();
                break;
                
            case CMD_WRITE_MEMORY:
                HandleWriteMemory();
                break;
                
            case CMD_SHUTDOWN:
                HandleShutdown();
                break;
                
            default:
                std::cerr << "Unknown command: 0x" << std::hex << (int)m_pSharedMemory->request_header.command << std::dec << std::endl;
                SendErrorResponse(STATUS_INVALID_PARAM);
                break;
        }
    }
    
    // 发送错误响应
    void SendErrorResponse(status_t status) {
        init_response_header(&m_pSharedMemory->response_header, status, 0);
        SetEvent(m_hResponseEvent);
    }
    
    // 发送成功响应
    void SendSuccessResponse(const void* data, uint32_t dataSize) {
        init_response_header(&m_pSharedMemory->response_header, STATUS_SUCCESS, dataSize);
        
        if (data && dataSize > 0) {
            memcpy(m_pSharedMemory->response_data, data, dataSize);
        }
        
        SetEvent(m_hResponseEvent);
    }
    
    // 处理初始化请求
    void HandleInit() {
        if (m_pSharedMemory->request_header.data_size < sizeof(init_request_t)) {
            std::cerr << "Invalid init request size" << std::endl;
            SendErrorResponse(STATUS_INVALID_PARAM);
            return;
        }
        
        init_request_t* request = (init_request_t*)m_pSharedMemory->request_data;
        uint32_t vmwareProcessId = request->vmware_process_id;
        
        std::cout << "Initializing VMware process: " << vmwareProcessId << std::endl;
        
        // 检查是否已经初始化
        {
            std::lock_guard<std::mutex> lock(m_vmmMutex);
            if (m_vmmHandles.find(vmwareProcessId) != m_vmmHandles.end()) {
                std::cout << "VMware process already initialized" << std::endl;
                SendSuccessResponse(NULL, 0);
                return;
            }
        }
        
        // 构造VMware设备参数
        std::string deviceArg = "vmware://ro=1,id=" + std::to_string(vmwareProcessId);
        
        LPCSTR argv[] = {
            "",
            "-device",
            deviceArg.c_str(),
            "-v"
        };
        
        // 初始化VMM
        VMM_HANDLE hVMM = VMMDLL_Initialize(4, argv);
        if (!hVMM) {
            std::cerr << "Failed to initialize VMware process: " << vmwareProcessId << std::endl;
            SendErrorResponse(STATUS_ERROR);
            return;
        }
        
        // 保存VMM句柄
        {
            std::lock_guard<std::mutex> lock(m_vmmMutex);
            m_vmmHandles[vmwareProcessId] = hVMM;
        }
        
        std::cout << "Successfully initialized VMware process: " << vmwareProcessId << std::endl;
        SendSuccessResponse(NULL, 0);
    }
    
    // 处理获取进程ID请求
    void HandleGetProcessId() {
        if (m_pSharedMemory->request_header.data_size < sizeof(get_process_id_request_t)) {
            std::cerr << "Invalid get process ID request size" << std::endl;
            SendErrorResponse(STATUS_INVALID_PARAM);
            return;
        }
        
        get_process_id_request_t* request = (get_process_id_request_t*)m_pSharedMemory->request_data;
        
        // 获取第一个VMM句柄
        VMM_HANDLE hVMM = NULL;
        {
            std::lock_guard<std::mutex> lock(m_vmmMutex);
            if (m_vmmHandles.empty()) {
                std::cerr << "No VMware process initialized" << std::endl;
                SendErrorResponse(STATUS_NOT_INITIALIZED);
                return;
            }
            hVMM = m_vmmHandles.begin()->second;
        }
        
        std::cout << "Getting process ID for: " << request->process_name << std::endl;
        
        // 获取进程ID
        DWORD dwPID = 0;
        BOOL result = VMMDLL_PidGetFromName(hVMM, (LPSTR)request->process_name, &dwPID);
        
        if (!result || dwPID == 0) {
            std::cerr << "Process not found: " << request->process_name << std::endl;
            SendErrorResponse(STATUS_NOT_FOUND);
            return;
        }
        
        // 发送响应
        get_process_id_response_t response;
        response.process_id = dwPID;
        
        std::cout << "Found process: " << request->process_name << " (PID: " << dwPID << ")" << std::endl;
        SendSuccessResponse(&response, sizeof(response));
    }
    
    // 处理获取模块基址请求
    void HandleGetModuleBase() {
        if (m_pSharedMemory->request_header.data_size < sizeof(get_module_base_request_t)) {
            std::cerr << "Invalid get module base request size" << std::endl;
            SendErrorResponse(STATUS_INVALID_PARAM);
            return;
        }
        
        get_module_base_request_t* request = (get_module_base_request_t*)m_pSharedMemory->request_data;
        
        // 获取第一个VMM句柄
        VMM_HANDLE hVMM = NULL;
        {
            std::lock_guard<std::mutex> lock(m_vmmMutex);
            if (m_vmmHandles.empty()) {
                std::cerr << "No VMware process initialized" << std::endl;
                SendErrorResponse(STATUS_NOT_INITIALIZED);
                return;
            }
            hVMM = m_vmmHandles.begin()->second;
        }
        
        std::cout << "Getting module base for: " << request->module_name 
                  << " in process: " << request->process_id << std::endl;
        
        // 获取模块基址
        ULONG64 vaBase = VMMDLL_ProcessGetModuleBaseU(hVMM, request->process_id, (LPSTR)request->module_name);
        
        if (vaBase == 0) {
            std::cerr << "Module not found: " << request->module_name << std::endl;
            SendErrorResponse(STATUS_NOT_FOUND);
            return;
        }
        
        // 发送响应
        get_module_base_response_t response;
        response.base_address = vaBase;
        
        std::cout << "Found module: " << request->module_name 
                  << " at 0x" << std::hex << vaBase << std::dec << std::endl;
        SendSuccessResponse(&response, sizeof(response));
    }
    
    // 处理读取内存请求
    void HandleReadMemory() {
        if (m_pSharedMemory->request_header.data_size < sizeof(memory_request_t)) {
            std::cerr << "Invalid read memory request size" << std::endl;
            SendErrorResponse(STATUS_INVALID_PARAM);
            return;
        }
        
        memory_request_t* request = (memory_request_t*)m_pSharedMemory->request_data;
        
        // 检查请求大小
        if (request->size > sizeof(m_pSharedMemory->response_data) - sizeof(memory_response_t)) {
            std::cerr << "Read request too large: " << request->size << " bytes" << std::endl;
            SendErrorResponse(STATUS_INVALID_PARAM);
            return;
        }
        
        // 获取第一个VMM句柄
        VMM_HANDLE hVMM = NULL;
        {
            std::lock_guard<std::mutex> lock(m_vmmMutex);
            if (m_vmmHandles.empty()) {
                std::cerr << "No VMware process initialized" << std::endl;
                SendErrorResponse(STATUS_NOT_INITIALIZED);
                return;
            }
            hVMM = m_vmmHandles.begin()->second;
        }
        
        std::cout << "Reading memory: PID=" << request->process_id 
                  << " Address=0x" << std::hex << request->address 
                  << " Size=" << std::dec << request->size << std::endl;
        
        // 准备响应
        memory_response_t response;
        uint8_t* buffer = m_pSharedMemory->response_data + sizeof(memory_response_t);
        
        // 读取内存
        BOOL result = VMMDLL_MemRead(hVMM, request->process_id, request->address, buffer, request->size);
        
        if (!result) {
            std::cerr << "Failed to read memory" << std::endl;
            SendErrorResponse(STATUS_ERROR);
            return;
        }
        
        // 发送响应
        response.bytes_processed = request->size;
        memcpy(m_pSharedMemory->response_data, &response, sizeof(response));
        
        std::cout << "Successfully read " << request->size << " bytes" << std::endl;
        SendSuccessResponse(NULL, sizeof(response) + request->size);
    }
    
    // 处理写入内存请求
    void HandleWriteMemory() {
        if (m_pSharedMemory->request_header.data_size < sizeof(memory_request_t)) {
            std::cerr << "Invalid write memory request size" << std::endl;
            SendErrorResponse(STATUS_INVALID_PARAM);
            return;
        }
        
        memory_request_t* request = (memory_request_t*)m_pSharedMemory->request_data;
        
        // 检查请求大小
        if (m_pSharedMemory->request_header.data_size < sizeof(memory_request_t) + request->size) {
            std::cerr << "Write request data too small" << std::endl;
            SendErrorResponse(STATUS_INVALID_PARAM);
            return;
        }
        
        // 获取第一个VMM句柄
        VMM_HANDLE hVMM = NULL;
        {
            std::lock_guard<std::mutex> lock(m_vmmMutex);
            if (m_vmmHandles.empty()) {
                std::cerr << "No VMware process initialized" << std::endl;
                SendErrorResponse(STATUS_NOT_INITIALIZED);
                return;
            }
            hVMM = m_vmmHandles.begin()->second;
        }
        
        std::cout << "Writing memory: PID=" << request->process_id 
                  << " Address=0x" << std::hex << request->address 
                  << " Size=" << std::dec << request->size << std::endl;
        
        // 获取写入数据
        uint8_t* data = m_pSharedMemory->request_data + sizeof(memory_request_t);
        
        // 写入内存
        BOOL result = VMMDLL_MemWrite(hVMM, request->process_id, request->address, data, request->size);
        
        if (!result) {
            std::cerr << "Failed to write memory" << std::endl;
            SendErrorResponse(STATUS_ERROR);
            return;
        }
        
        // 发送响应
        memory_response_t response;
        response.bytes_processed = request->size;
        
        std::cout << "Successfully wrote " << request->size << " bytes" << std::endl;
        SendSuccessResponse(&response, sizeof(response));
    }
    
    // 处理关闭服务器请求
    void HandleShutdown() {
        std::cout << "Received shutdown command" << std::endl;
        SendSuccessResponse(NULL, 0);
        m_running = false;
    }
};

// 全局服务器实例
std::unique_ptr<MemProcFSServer> g_server;

// 控制台处理函数
BOOL WINAPI ConsoleCtrlHandler(DWORD dwCtrlType) {
    switch (dwCtrlType) {
        case CTRL_C_EVENT:
        case CTRL_BREAK_EVENT:
        case CTRL_CLOSE_EVENT:
            if (g_server) {
                std::cout << "Shutting down server..." << std::endl;
                g_server->Stop();
            }
            return TRUE;
        default:
            return FALSE;
    }
}

// 主函数
int main(int argc, char* argv[]) {
    // 设置控制台处理函数
    SetConsoleCtrlHandler(ConsoleCtrlHandler, TRUE);

    // 创建服务器实例
    g_server = std::make_unique<MemProcFSServer>();

    // 初始化服务器
    if (!g_server->Initialize()) {
        std::cerr << "Failed to initialize server" << std::endl;
        return 1;
    }

    // 启动服务器
    g_server->Start();

    // 等待服务器停止
    while (g_server) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    return 0;
}
