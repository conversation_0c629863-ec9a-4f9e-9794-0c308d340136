@echo off
echo MemProcFS TCP Server v2.0 - Build Test
echo =====================================

:: 测试协议头文件编译
echo Testing protocol header compilation...
echo #include "protocol.h" > test_protocol.cpp
echo int main() { return 0; } >> test_protocol.cpp

cl /EHsc test_protocol.cpp /Fe:test_protocol.exe >nul 2>&1
if errorlevel 1 (
    echo Error: Protocol header compilation failed
    del test_protocol.cpp >nul 2>&1
    exit /b 1
) else (
    echo ✓ Protocol header compilation: OK
    del test_protocol.cpp test_protocol.exe >nul 2>&1
)

:: 测试编译修复
echo Testing compile fixes...
cl test_compile_fix.c /Fe:test_compile_fix.exe >nul 2>&1
if errorlevel 1 (
    echo Error: Compile fix test failed
    exit /b 1
) else (
    echo ✓ Compile fixes: OK
    test_compile_fix.exe
    del test_compile_fix.exe >nul 2>&1
)

:: 测试C客户端编译
echo Testing C client compilation...
cl client_c.c ws2_32.lib /Fe:test_client_c.exe >nul 2>&1
if errorlevel 1 (
    echo Error: C client compilation failed
    exit /b 1
) else (
    echo ✓ C client compilation: OK
    del test_client_c.exe >nul 2>&1
)

:: 测试调试客户端编译
echo Testing debug client compilation...
cl debug_client.c ws2_32.lib /Fe:test_debug_client.exe >nul 2>&1
if errorlevel 1 (
    echo Error: Debug client compilation failed
    exit /b 1
) else (
    echo ✓ Debug client compilation: OK
    del test_debug_client.exe >nul 2>&1
)

:: 测试单包测试客户端编译
echo Testing single packet test client compilation...
cl test_single_packet.c ws2_32.lib /Fe:test_single_packet_test.exe >nul 2>&1
if errorlevel 1 (
    echo Error: Single packet test client compilation failed
    exit /b 1
) else (
    echo ✓ Single packet test client compilation: OK
    del test_single_packet_test.exe >nul 2>&1
)

:: 测试Python客户端语法
echo Testing Python client syntax...
python -m py_compile client_python_simple.py >nul 2>&1
if errorlevel 1 (
    echo Error: Python client syntax error
    exit /b 1
) else (
    echo ✓ Python client syntax: OK
    del __pycache__ /s /q >nul 2>&1
    rmdir __pycache__ >nul 2>&1
)

:: 清理临时文件
del *.obj >nul 2>&1

echo.
echo All tests passed!
echo.
echo Ready to build with:
echo   build.bat "C:\path\to\MemProcFS"
echo.
echo Or with CMake:
echo   mkdir build
echo   cd build
echo   cmake .. -DMEMPROCFS_ROOT="C:\path\to\MemProcFS"
echo   cmake --build . --config Release
