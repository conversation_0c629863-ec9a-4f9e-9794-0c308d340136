# MemProcFS Shared Memory Server v2.0

高性能共享内存通信的MemProcFS服务器，支持多VMware进程访问。

## ✨ 主要特性

- **共享内存通信**: 高性能的进程间通信，比TCP更快
- **简化协议**: 精简的数据头和响应头，减少开销
- **服务端处理**: 所有复杂逻辑在服务端处理，客户端操作简单
- **多进程支持**: 支持多个VMware进程的并发访问
- **跨语言支持**: 提供C和Python客户端示例
- **Windows专用**: 专为Windows系统优化

## 🚀 快速开始

### 1. 构建

```bash
# 使用Visual Studio Developer Command Prompt
build_shm.bat "C:\MemProcFS"
```

### 2. 运行服务器

```bash
cd bin\Release
memprocfs_server_shm.exe
```

### 3. 测试客户端

**C客户端**:
```bash
client_shm.exe
```

**Python客户端**:
```bash
python client_shm.py
```

## 📋 系统要求

- Windows 10/11 (64位)
- Visual Studio 2019/2022 (或Build Tools)
- MemProcFS库文件
- VMware Workstation/Player (运行中的虚拟机)

## 🔧 协议说明

### 共享内存布局

```
共享内存 (1MB)
├── 请求区域 (前512KB)
│   ├── request_header_t (16字节)
│   └── request_data (剩余空间)
└── 响应区域 (后512KB)
    ├── response_header_t (12字节)
    └── response_data (剩余空间)
```

### 简化的数据结构

**请求头** (16字节):
```c
typedef struct {
    uint32_t magic;         // Magic number
    uint8_t  version;       // Protocol version
    uint8_t  command;       // Command type
    uint16_t reserved;      // Reserved
    uint32_t data_size;     // Data size
    uint32_t sequence;      // Sequence number
} request_header_t;
```

**响应头** (12字节):
```c
typedef struct {
    uint32_t magic;         // Magic number
    uint8_t  status;        // Status code
    uint8_t  reserved[3];   // Reserved
    uint32_t data_size;     // Data size
} response_header_t;
```

### 支持的命令

| 命令 | 描述 | 客户端API |
|------|------|-----------|
| CMD_INIT | 初始化VMware进程 | `memprocfs_init()` |
| CMD_GET_PROCESS_ID | 获取进程ID | `memprocfs_get_process_id()` |
| CMD_GET_MODULE_BASE | 获取模块基址 | `memprocfs_get_module_base()` |
| CMD_READ_MEMORY | 读取内存 | `memprocfs_read_memory()` |
| CMD_WRITE_MEMORY | 写入内存 | `memprocfs_write_memory()` |

## 💻 客户端API

### C客户端

```c
#include "shared_memory_protocol.h"

// 初始化客户端
memprocfs_client_t client;
if (!memprocfs_init(&client, vmware_process_id)) {
    // 初始化失败
}

// 获取进程ID
uint32_t pid = memprocfs_get_process_id(&client, "notepad.exe");

// 获取模块基址
uint64_t base = memprocfs_get_module_base(&client, pid, "ntdll.dll");

// 读取内存
uint8_t buffer[1024];
int bytes_read = memprocfs_read_memory(&client, pid, address, buffer, sizeof(buffer));

// 写入内存
int bytes_written = memprocfs_write_memory(&client, pid, address, data, size);

// 清理
memprocfs_cleanup(&client);
```

### Python客户端

```python
from client_shm import MemProcFSClient

# 初始化客户端
client = MemProcFSClient(vmware_process_id)
if not client.connect():
    # 连接失败

# 获取进程ID
pid = client.get_process_id("notepad.exe")

# 获取模块基址
base = client.get_module_base(pid, "ntdll.dll")

# 读取内存
data = client.read_memory(pid, address, size)

# 写入内存
bytes_written = client.write_memory(pid, address, data)

# 断开连接
client.disconnect()
```

## 🔄 通信流程

1. **客户端连接**: 打开共享内存和同步对象
2. **初始化**: 发送VMware进程ID给服务器
3. **请求处理**: 
   - 客户端获取互斥锁
   - 写入请求到共享内存
   - 触发请求事件
   - 释放互斥锁
   - 等待响应事件
4. **响应处理**: 从共享内存读取响应数据
5. **清理**: 关闭所有句柄

## 📁 项目结构

```
MemProcFS_SharedMemory/
├── memprocfs_server_shm.cpp    # 共享内存服务器
├── client_shm.c                # C客户端
├── client_shm.py               # Python客户端
├── shared_memory_protocol.h    # 协议定义
├── build_shm.bat               # 构建脚本
├── test_shm.bat                # 测试脚本
└── README_SHM.md               # 项目文档
```

## 🔒 安全注意事项

- 仅在受控环境中使用
- 确保VMware虚拟机的安全性
- 定期更新MemProcFS库
- 监控内存访问操作

## 🐛 故障排除

### 常见问题

1. **"Failed to open shared memory"**
   - 确保服务器正在运行
   - 检查Windows权限

2. **"Failed to initialize VMware process"**
   - 确认VMware进程ID正确
   - 检查VMware虚拟机状态

3. **编译错误**
   - 使用Visual Studio Developer Command Prompt
   - 检查MemProcFS路径

### 调试技巧

- 查看服务器控制台输出
- 使用Process Monitor监控文件/注册表访问
- 检查Windows事件日志

## 📄 许可证

本项目仅供学习和研究使用。请遵守相关法律法规。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**注意**: 此工具仅用于合法的安全研究和教育目的。使用者需要确保遵守所有适用的法律法规。
