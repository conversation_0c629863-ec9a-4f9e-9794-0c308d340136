/*
 * 测试单包发送功能
 * 验证服务器是否将响应头和数据合并成一个包发送
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    typedef int socklen_t;
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define closesocket close
#endif

#include "protocol.h"

typedef struct {
    SOCKET socket;
    uint32_t sequence;
} test_client_t;

// 发送数据
int send_data(SOCKET socket, const void* data, uint32_t size) {
    const uint8_t* bytes = (const uint8_t*)data;
    uint32_t sent = 0;
    
    while (sent < size) {
        int result = send(socket, (const char*)(bytes + sent), size - sent, 0);
        if (result <= 0) {
            return 0;
        }
        sent += result;
    }
    return 1;
}

// 接收数据
int receive_data(SOCKET socket, void* data, uint32_t size) {
    uint8_t* bytes = (uint8_t*)data;
    uint32_t received = 0;
    
    while (received < size) {
        int result = recv(socket, (char*)(bytes + received), size - received, 0);
        if (result <= 0) {
            return 0;
        }
        received += result;
    }
    return 1;
}

// 测试单包接收
int test_single_packet_receive(test_client_t* client) {
    printf("测试单包接收功能\n");
    printf("================\n");
    
    // 发送初始化命令
    packet_header_t header;
    header.magic = PACKET_MAGIC;
    header.version = MEMPROCFS_PROTOCOL_VERSION;
    header.command = CMD_INIT;
    header.flags = 0;
    header.data_size = sizeof(uint32_t);
    header.sequence = client->sequence++;
    
    uint32_t vmware_pid = 9856;
    
    printf("发送初始化命令...\n");
    if (!send_data(client->socket, &header, sizeof(header))) {
        printf("✗ 发送初始化头失败\n");
        return 0;
    }
    
    if (!send_data(client->socket, &vmware_pid, sizeof(vmware_pid))) {
        printf("✗ 发送VMware进程ID失败\n");
        return 0;
    }
    
    // 接收响应 - 应该是一个完整的包
    printf("接收响应包...\n");
    
    // 首先接收数据包头
    packet_header_t response_header;
    if (!receive_data(client->socket, &response_header, sizeof(response_header))) {
        printf("✗ 接收响应包头失败\n");
        return 0;
    }
    
    printf("响应包头信息:\n");
    printf("  Magic: 0x%08X\n", response_header.magic);
    printf("  Version: %u\n", response_header.version);
    printf("  Command: 0x%02X\n", response_header.command);
    printf("  Data Size: %u\n", response_header.data_size);
    printf("  Sequence: %u\n", response_header.sequence);
    
    // 接收响应数据（应该包含response_header_t）
    if (response_header.data_size > 0) {
        uint8_t* response_data = malloc(response_header.data_size);
        if (!response_data) {
            printf("✗ 内存分配失败\n");
            return 0;
        }
        
        if (!receive_data(client->socket, response_data, response_header.data_size)) {
            printf("✗ 接收响应数据失败\n");
            free(response_data);
            return 0;
        }
        
        // 解析响应头
        response_header_t* resp_hdr = (response_header_t*)response_data;
        printf("响应数据头信息:\n");
        printf("  Status: %u (%s)\n", resp_hdr->status, 
               resp_hdr->status == STATUS_SUCCESS ? "SUCCESS" : "ERROR");
        printf("  Data Size: %u\n", resp_hdr->data_size);
        
        free(response_data);
        
        if (resp_hdr->status == STATUS_SUCCESS) {
            printf("✓ 初始化成功，服务器使用单包发送\n");
            return 1;
        } else {
            printf("✗ 初始化失败\n");
            return 0;
        }
    } else {
        printf("✗ 响应数据大小为0\n");
        return 0;
    }
}

int main() {
    printf("MemProcFS TCP服务器 - 单包发送测试\n");
    printf("=================================\n\n");
    
#ifdef _WIN32
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        printf("WSAStartup失败\n");
        return 1;
    }
#endif

    test_client_t client;
    client.socket = socket(AF_INET, SOCK_STREAM, 0);
    if (client.socket == INVALID_SOCKET) {
        printf("创建socket失败\n");
#ifdef _WIN32
        WSACleanup();
#endif
        return 1;
    }

    struct sockaddr_in server_addr;
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(12345);
    inet_pton(AF_INET, "127.0.0.1", &server_addr.sin_addr);

    printf("连接到服务器...\n");
    if (connect(client.socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        printf("连接失败\n");
        closesocket(client.socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 1;
    }

    client.sequence = 1;
    printf("✓ 连接成功\n\n");
    
    // 测试单包接收
    int result = test_single_packet_receive(&client);
    
    closesocket(client.socket);
#ifdef _WIN32
    WSACleanup();
#endif
    
    if (result) {
        printf("\n✓ 单包发送测试通过！\n");
        printf("服务器正确地将响应头和数据合并成一个包发送。\n");
    } else {
        printf("\n✗ 单包发送测试失败！\n");
    }
    
    return result ? 0 : 1;
}
