/*
 * 调试版本的C客户端
 * 显示详细的通信过程
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    typedef int socklen_t;
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define closesocket close
#endif

#include "protocol.h"

typedef struct {
    SOCKET socket;
    uint32_t sequence;
} memprocfs_client_t;

// 调试函数：打印数据包内容
void print_packet_header(const char* prefix, const packet_header_t* header) {
    printf("%s Packet Header:\n", prefix);
    printf("  Magic: 0x%08X\n", header->magic);
    printf("  Version: %u\n", header->version);
    printf("  Command: 0x%02X\n", header->command);
    printf("  Flags: 0x%02X\n", header->flags);
    printf("  Data Size: %u\n", header->data_size);
    printf("  Sequence: %u\n", header->sequence);
}

void print_response_header(const response_header_t* header) {
    printf("Response Header:\n");
    printf("  Status: %u (%s)\n", header->status, 
           header->status == STATUS_SUCCESS ? "SUCCESS" : "ERROR");
    printf("  Reserved: %u %u %u\n", header->reserved[0], header->reserved[1], header->reserved[2]);
    printf("  Data Size: %u\n", header->data_size);
}

// 发送数据
int send_data(SOCKET socket, const void* data, uint32_t size) {
    const uint8_t* bytes = (const uint8_t*)data;
    uint32_t sent = 0;
    
    printf("发送 %u 字节: ", size);
    for (uint32_t i = 0; i < size && i < 16; i++) {
        printf("%02X ", bytes[i]);
    }
    if (size > 16) printf("...");
    printf("\n");
    
    while (sent < size) {
        int result = send(socket, (const char*)(bytes + sent), size - sent, 0);
        if (result <= 0) {
            printf("发送失败: %d\n", result);
            return 0;
        }
        sent += result;
    }
    return 1;
}

// 接收数据
int receive_data(SOCKET socket, void* data, uint32_t size) {
    uint8_t* bytes = (uint8_t*)data;
    uint32_t received = 0;
    
    while (received < size) {
        int result = recv(socket, (char*)(bytes + received), size - received, 0);
        if (result <= 0) {
            printf("接收失败: %d\n", result);
            return 0;
        }
        received += result;
    }
    
    printf("接收 %u 字节: ", size);
    for (uint32_t i = 0; i < size && i < 16; i++) {
        printf("%02X ", bytes[i]);
    }
    if (size > 16) printf("...");
    printf("\n");
    
    return 1;
}

// 初始化客户端
int debug_client_init(memprocfs_client_t* client, const char* server_ip, uint16_t port, uint32_t vmware_process_id) {
    printf("=== 初始化客户端 ===\n");
    
#ifdef _WIN32
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        printf("WSAStartup 失败\n");
        return 0;
    }
#endif

    client->socket = socket(AF_INET, SOCK_STREAM, 0);
    if (client->socket == INVALID_SOCKET) {
        printf("创建socket失败\n");
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }

    struct sockaddr_in server_addr;
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    inet_pton(AF_INET, server_ip, &server_addr.sin_addr);

    if (connect(client->socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        printf("连接失败\n");
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }

    client->sequence = 1;
    printf("✓ TCP连接成功到 %s:%d\n", server_ip, port);
    
    // 发送初始化命令
    printf("\n=== 发送初始化命令 ===\n");
    packet_header_t header;
    header.magic = PACKET_MAGIC;
    header.version = MEMPROCFS_PROTOCOL_VERSION;
    header.command = CMD_INIT;
    header.flags = 0;
    header.data_size = sizeof(uint32_t);
    header.sequence = client->sequence++;
    
    print_packet_header("发送", &header);
    
    if (!send_data(client->socket, &header, sizeof(header))) {
        printf("发送初始化头失败\n");
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }
    
    printf("发送VMware进程ID: %u\n", vmware_process_id);
    if (!send_data(client->socket, &vmware_process_id, sizeof(vmware_process_id))) {
        printf("发送VMware进程ID失败\n");
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }
    
    // 接收响应
    printf("\n=== 接收初始化响应 ===\n");
    packet_header_t response_header;
    if (!receive_data(client->socket, &response_header, sizeof(response_header))) {
        printf("接收响应头失败\n");
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }
    
    print_packet_header("接收", &response_header);
    
    if (response_header.data_size > 0) {
        uint8_t response_data[64];
        if (!receive_data(client->socket, response_data, response_header.data_size)) {
            printf("接收响应数据失败\n");
            closesocket(client->socket);
#ifdef _WIN32
            WSACleanup();
#endif
            return 0;
        }
        
        response_header_t* resp_hdr = (response_header_t*)response_data;
        print_response_header(resp_hdr);
        
        if (resp_hdr->status != STATUS_SUCCESS) {
            printf("✗ 服务器初始化失败\n");
            closesocket(client->socket);
#ifdef _WIN32
            WSACleanup();
#endif
            return 0;
        }
    }
    
    printf("✓ 初始化成功\n");
    return 1;
}

int main() {
    printf("MemProcFS TCP客户端 - 调试版本\n");
    printf("==============================\n\n");
    
    memprocfs_client_t client;
    uint32_t vmware_pid = 9856;
    
    if (!debug_client_init(&client, "127.0.0.1", 12345, vmware_pid)) {
        printf("初始化失败\n");
        return 1;
    }
    
    printf("\n=== 测试获取进程ID ===\n");
    
    // 手动发送获取进程请求
    packet_header_t header;
    header.magic = PACKET_MAGIC;
    header.version = MEMPROCFS_PROTOCOL_VERSION;
    header.command = CMD_GET_PROCESS_BY_NAME;
    header.flags = 0;
    header.data_size = sizeof(process_by_name_request_t);
    header.sequence = client.sequence++;
    
    process_by_name_request_t request;
    strncpy(request.process_name, "notepad.exe", sizeof(request.process_name) - 1);
    request.process_name[sizeof(request.process_name) - 1] = '\0';
    
    print_packet_header("发送", &header);
    printf("进程名: %s\n", request.process_name);
    
    if (!send_data(client.socket, &header, sizeof(header))) {
        printf("发送请求头失败\n");
        return 1;
    }
    
    if (!send_data(client.socket, &request, sizeof(request))) {
        printf("发送请求数据失败\n");
        return 1;
    }
    
    // 接收响应
    packet_header_t response_header;
    if (!receive_data(client.socket, &response_header, sizeof(response_header))) {
        printf("接收响应头失败\n");
        return 1;
    }
    
    print_packet_header("接收", &response_header);
    
    if (response_header.data_size > 0) {
        uint8_t response_data[256];
        if (!receive_data(client.socket, response_data, response_header.data_size)) {
            printf("接收响应数据失败\n");
            return 1;
        }
        
        response_header_t* resp_hdr = (response_header_t*)response_data;
        print_response_header(resp_hdr);
        
        if (resp_hdr->status == STATUS_SUCCESS) {
            process_by_name_response_t* proc_resp = (process_by_name_response_t*)(response_data + sizeof(response_header_t));
            printf("✓ 找到进程ID: %u\n", proc_resp->process_id);
        } else {
            printf("✗ 获取进程ID失败\n");
        }
    }
    
    closesocket(client.socket);
#ifdef _WIN32
    WSACleanup();
#endif
    
    printf("\n调试完成\n");
    return 0;
}
