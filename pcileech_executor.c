/*
 * PCILeech风格的代码执行器
 * 
 * 基于MemProcFS + PCILeech shellcode的代码执行示例
 * 展示如何在VMware虚拟机中执行内核和用户态代码
 */

#define _CRT_SECURE_NO_WARNINGS
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>

#ifdef _WIN32
    #include <windows.h>
#else
    #error "This example only supports Windows"
#endif

#include "shared_memory_protocol.h"
#include "pcileech_shellcode_examples.h"

// PCILeech执行器结构
typedef struct {
    memprocfs_client_t* client;
    uint32_t target_pid;
    uint64_t kernel_base;
    uint64_t ntdll_base;
    uint64_t kernel32_base;
} pcileech_executor_t;

// 初始化PCILeech执行器
int pcileech_init(pcileech_executor_t* executor, memprocfs_client_t* client, uint32_t target_pid) {
    if (!executor || !client) return 0;
    
    executor->client = client;
    executor->target_pid = target_pid;
    
    // 获取关键模块基址
    executor->ntdll_base = memprocfs_get_module_base(client, target_pid, "ntdll.dll");
    executor->kernel32_base = memprocfs_get_module_base(client, target_pid, "kernel32.dll");
    
    printf("PCILeech Executor initialized:\n");
    printf("  Target PID: %u\n", target_pid);
    printf("  ntdll.dll: 0x%llX\n", executor->ntdll_base);
    printf("  kernel32.dll: 0x%llX\n", executor->kernel32_base);
    
    return (executor->ntdll_base > 0 && executor->kernel32_base > 0);
}

// 执行shellcode的通用函数
int pcileech_execute_shellcode(pcileech_executor_t* executor, const char* shellcode_name, void* params, uint32_t param_size) {
    const shellcode_info_t* shellcode = get_shellcode_by_name(shellcode_name);
    if (!shellcode) {
        printf("Shellcode '%s' not found\n", shellcode_name);
        return 0;
    }
    
    printf("\nExecuting shellcode: %s\n", shellcode->name);
    printf("Description: %s\n", shellcode->description);
    printf("Size: %u bytes\n", shellcode->size);
    printf("Flags: 0x%08X\n", shellcode->flags);
    
    // 在目标进程中找一个可写的内存区域
    uint64_t shellcode_addr = 0x10000000; // 简化：使用固定地址
    
    // 写入shellcode
    int result = memprocfs_write_memory(executor->client, executor->target_pid, 
                                      shellcode_addr, shellcode->code, shellcode->size);
    if (!result) {
        printf("Failed to write shellcode to target process\n");
        return 0;
    }
    
    printf("Shellcode written to: 0x%llX\n", shellcode_addr);
    
    // 如果有参数，写入参数
    if (params && param_size > 0) {
        uint64_t param_addr = shellcode_addr + shellcode->size + 0x100;
        result = memprocfs_write_memory(executor->client, executor->target_pid,
                                      param_addr, params, param_size);
        if (!result) {
            printf("Failed to write parameters\n");
            return 0;
        }
        printf("Parameters written to: 0x%llX\n", param_addr);
    }
    
    printf("Shellcode execution simulated (real execution requires advanced techniques)\n");
    printf("In a real implementation, you would:\n");
    printf("1. Find or allocate executable memory\n");
    printf("2. Set proper memory permissions (RWX)\n");
    printf("3. Create a remote thread or hijack existing thread\n");
    printf("4. Execute the shellcode and get results\n");
    
    return 1;
}

// 演示内核信息获取
void demo_kernel_info(pcileech_executor_t* executor) {
    printf("\n=== Kernel Information Demo ===\n");
    pcileech_execute_shellcode(executor, "kernel_info", NULL, 0);
}

// 演示用户态MessageBox
void demo_user_msgbox(pcileech_executor_t* executor) {
    printf("\n=== User Mode MessageBox Demo ===\n");
    
    // 需要解析MessageBoxA地址
    uint64_t user32_base = memprocfs_get_module_base(executor->client, executor->target_pid, "user32.dll");
    if (user32_base == 0) {
        printf("user32.dll not loaded in target process\n");
        return;
    }
    
    printf("user32.dll base: 0x%llX\n", user32_base);
    
    // 在真实实现中，需要解析MessageBoxA的导出地址
    uint64_t msgbox_addr = user32_base + 0x1000; // 示例偏移
    
    // 修改shellcode中的MessageBoxA地址
    uint8_t modified_shellcode[sizeof(user_msgbox_shellcode)];
    memcpy(modified_shellcode, user_msgbox_shellcode, sizeof(user_msgbox_shellcode));
    
    // 在偏移0x20处写入MessageBoxA地址（需要根据实际shellcode调整）
    // *(uint64_t*)(modified_shellcode + 0x20) = msgbox_addr;
    
    pcileech_execute_shellcode(executor, "user_msgbox", NULL, 0);
}

// 演示内存搜索
void demo_memory_search(pcileech_executor_t* executor) {
    printf("\n=== Memory Search Demo ===\n");
    
    // 搜索参数
    struct {
        uint64_t start_addr;
        uint64_t end_addr;
        uint8_t pattern[16];
        uint32_t pattern_size;
    } search_params = {
        .start_addr = executor->ntdll_base,
        .end_addr = executor->ntdll_base + 0x100000,
        .pattern = {0x4D, 0x5A}, // MZ header
        .pattern_size = 2
    };
    
    printf("Searching for MZ header in ntdll.dll...\n");
    pcileech_execute_shellcode(executor, "memory_search", &search_params, sizeof(search_params));
}

// 演示进程隐藏
void demo_process_hide(pcileech_executor_t* executor) {
    printf("\n=== Process Hide Demo ===\n");
    printf("WARNING: This is a dangerous operation that modifies kernel structures!\n");
    printf("Target PID: %u\n", executor->target_pid);
    
    uint32_t target_pid = executor->target_pid;
    pcileech_execute_shellcode(executor, "process_hide", &target_pid, sizeof(target_pid));
}

// 演示权限提升
void demo_privilege_escalation(pcileech_executor_t* executor) {
    printf("\n=== Privilege Escalation Demo ===\n");
    printf("WARNING: This operation modifies process tokens!\n");
    printf("Target PID: %u\n", executor->target_pid);
    
    uint32_t target_pid = executor->target_pid;
    pcileech_execute_shellcode(executor, "privilege_escalation", &target_pid, sizeof(target_pid));
}

// 演示内存转储
void demo_memory_dump(pcileech_executor_t* executor) {
    printf("\n=== Memory Dump Demo ===\n");
    
    // 转储参数
    struct {
        uint64_t source_addr;
        uint64_t dest_addr;
        uint32_t size;
    } dump_params = {
        .source_addr = executor->ntdll_base,
        .dest_addr = 0x20000000, // 目标地址
        .size = 0x1000 // 4KB
    };
    
    printf("Dumping 4KB from ntdll.dll base...\n");
    pcileech_execute_shellcode(executor, "memory_dump", &dump_params, sizeof(dump_params));
    
    // 读取转储的数据进行验证
    uint8_t buffer[0x100];
    if (memprocfs_read_memory(executor->client, executor->target_pid, 
                            executor->ntdll_base, buffer, sizeof(buffer))) {
        printf("First 16 bytes of ntdll.dll:\n");
        for (int i = 0; i < 16; i++) {
            printf("%02X ", buffer[i]);
        }
        printf("\n");
    }
}

// 主程序
int main() {
    printf("PCILeech-style Code Executor\n");
    printf("============================\n");
    printf("Based on MemProcFS + PCILeech shellcode examples\n\n");
    
    // 列出可用的shellcode
    list_available_shellcodes();
    
    // 初始化MemProcFS客户端
    memprocfs_client_t client;
    uint32_t vmware_pid = 12280; // 使用实际的VMware进程ID
    
    printf("Initializing MemProcFS client with VMware PID: %u\n", vmware_pid);
    if (!memprocfs_init(&client, vmware_pid)) {
        printf("Failed to initialize MemProcFS client\n");
        return 1;
    }
    
    // 获取目标进程
    uint32_t target_pid = memprocfs_get_process_id(&client, "notepad.exe");
    if (target_pid == 0) {
        printf("Target process 'notepad.exe' not found\n");
        printf("Please start notepad.exe in the virtual machine\n");
        memprocfs_cleanup(&client);
        return 1;
    }
    
    // 初始化PCILeech执行器
    pcileech_executor_t executor;
    if (!pcileech_init(&executor, &client, target_pid)) {
        printf("Failed to initialize PCILeech executor\n");
        memprocfs_cleanup(&client);
        return 1;
    }
    
    // 执行各种演示
    demo_kernel_info(&executor);
    demo_user_msgbox(&executor);
    demo_memory_search(&executor);
    demo_memory_dump(&executor);
    
    // 危险操作（仅演示，实际使用需谨慎）
    printf("\n=== WARNING: Dangerous Operations ===\n");
    printf("The following operations are potentially dangerous and are only demonstrated.\n");
    printf("In a real environment, they could cause system instability.\n");
    
    char choice;
    printf("Do you want to see dangerous operation demos? (y/N): ");
    scanf(" %c", &choice);
    
    if (choice == 'y' || choice == 'Y') {
        demo_process_hide(&executor);
        demo_privilege_escalation(&executor);
    }
    
    // 清理
    memprocfs_cleanup(&client);
    
    printf("\nPCILeech-style execution demo completed.\n");
    printf("\nNote: This is a demonstration of PCILeech concepts.\n");
    printf("Real implementation requires:\n");
    printf("1. Proper memory allocation and permission setting\n");
    printf("2. Thread creation or hijacking mechanisms\n");
    printf("3. Kernel-level access for kernel shellcodes\n");
    printf("4. Advanced anti-detection techniques\n");
    
    return 0;
}
