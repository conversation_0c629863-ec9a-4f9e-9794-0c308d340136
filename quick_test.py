#!/usr/bin/env python3
"""
MemProcFS TCP Server v2.0 - 快速测试脚本
测试多VMware进程支持和简化API
"""

import socket
import struct
import time
import threading

def test_connection(vmware_pid, test_name):
    """测试连接到指定VMware进程"""
    print(f"\n=== {test_name} (VMware PID: {vmware_pid}) ===")
    
    try:
        # 连接到服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect(("127.0.0.1", 12345))
        print(f"✓ TCP连接成功")
        
        # 发送初始化命令
        header = struct.pack('<IBBHII', 0x4D505246, 1, 0x00, 0, 4, 1)
        sock.send(header)
        sock.send(struct.pack('<I', vmware_pid))
        
        # 接收响应
        response_header = sock.recv(16)
        response_data = sock.recv(8)
        
        status = struct.unpack('<B', response_data[:1])[0]
        if status == 0:
            print(f"✓ VMware进程 {vmware_pid} 初始化成功")
        else:
            print(f"✗ VMware进程 {vmware_pid} 初始化失败")
            sock.close()
            return False
        
        # 测试获取进程列表
        print("测试获取进程列表...")
        header = struct.pack('<IBBHII', 0x4D505246, 1, 0x20, 0, 0, 2)
        sock.send(header)
        
        response_header = sock.recv(16)
        response_data = sock.recv(8)
        
        status = struct.unpack('<B', response_data[:1])[0]
        if status == 0:
            count = struct.unpack('<I', response_data[4:8])[0]
            print(f"✓ 获取进程列表成功，共 {count} 个进程")
        else:
            print(f"✗ 获取进程列表失败")
        
        sock.close()
        print(f"✓ 连接关闭")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    print("MemProcFS TCP Server v2.0 - 快速测试")
    print("====================================")
    
    print("\n请确保:")
    print("1. MemProcFS服务器正在运行")
    print("2. VMware正在运行多个虚拟机")
    print("3. 已知VMware进程的PID")
    
    # 测试不同的VMware进程ID
    test_cases = [
        (9856, "测试客户端A"),
        (1234, "测试客户端B"),
        (5678, "测试客户端C")
    ]
    
    print(f"\n开始测试多客户端连接...")
    
    # 并发测试
    threads = []
    for vmware_pid, test_name in test_cases:
        thread = threading.Thread(target=test_connection, args=(vmware_pid, test_name))
        threads.append(thread)
        thread.start()
        time.sleep(0.5)  # 错开连接时间
    
    # 等待所有测试完成
    for thread in threads:
        thread.join()
    
    print(f"\n=== 测试完成 ===")
    print("检查服务器控制台输出，应该看到:")
    print("- 多个客户端连接日志")
    print("- 每个客户端的VMware进程初始化")
    print("- 详细的操作日志和结果")

if __name__ == "__main__":
    main()
