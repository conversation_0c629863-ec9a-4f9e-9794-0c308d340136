@echo off
setlocal enabledelayedexpansion

echo MemProcFS TCP Server v2.0 - Windows Build
echo ========================================

:: 检查参数
if "%~1"=="" (
    echo Usage: build.bat "C:\path\to\MemProcFS"
    echo.
    echo Example: build.bat "C:\MemProcFS"
    echo.
    echo The MemProcFS directory should contain:
    echo   - vmmdll.h
    echo   - vmmdll.lib
    echo   - vmmdll.dll
    exit /b 1
)

set MEMPROCFS_PATH=%~1

:: 验证MemProcFS路径
if not exist "%MEMPROCFS_PATH%\vmmdll.h" (
    echo Error: vmmdll.h not found in %MEMPROCFS_PATH%
    exit /b 1
)

if not exist "%MEMPROCFS_PATH%\vmmdll.lib" (
    echo Error: vmmdll.lib not found in %MEMPROCFS_PATH%
    exit /b 1
)

if not exist "%MEMPROCFS_PATH%\vmmdll.dll" (
    echo Error: vmmdll.dll not found in %MEMPROCFS_PATH%
    exit /b 1
)

echo MemProcFS path: %MEMPROCFS_PATH%

:: 检查编译器
where cl >nul 2>&1
if errorlevel 1 (
    echo Error: Visual Studio compiler (cl.exe) not found
    echo Please run this script from a Visual Studio Developer Command Prompt
    exit /b 1
)

:: 创建输出目录
if not exist "bin" mkdir bin
if not exist "bin\Release" mkdir bin\Release

echo.
echo Building MemProcFS TCP Server...

:: 编译服务器
cl /EHsc /O2 /MD ^
   /I"%MEMPROCFS_PATH%" ^
   memprocfs_server_native.cpp ^
   /link "%MEMPROCFS_PATH%\vmmdll.lib" ws2_32.lib ^
   /OUT:bin\Release\memprocfs_server.exe

if errorlevel 1 (
    echo.
    echo Server build failed!
    echo.
    echo Common issues:
    echo 1. Make sure you're running from Visual Studio Developer Command Prompt
    echo 2. Check that MemProcFS path is correct: %MEMPROCFS_PATH%
    echo 3. Verify vmmdll.lib exists in the MemProcFS directory
    exit /b 1
)

:: 编译C客户端
echo.
echo Building C client...
cl /EHsc /O2 /MD ^
   client_c.c ^
   /link ws2_32.lib ^
   /OUT:bin\Release\client_c.exe

if errorlevel 1 (
    echo.
    echo C client build failed!
    exit /b 1
)

:: 编译调试客户端
echo.
echo Building debug client...
cl /EHsc /O2 /MD ^
   debug_client.c ^
   /link ws2_32.lib ^
   /OUT:bin\Release\debug_client.exe

if errorlevel 1 (
    echo.
    echo Debug client build failed!
    exit /b 1
)

:: 编译测试客户端
echo.
echo Building test client...
cl /EHsc /O2 /MD ^
   test_simple.c ^
   /link ws2_32.lib ^
   /OUT:bin\Release\test_simple.exe

if errorlevel 1 (
    echo.
    echo Test client build failed!
    exit /b 1
)

:: 复制必要文件
echo.
echo Copying files...

copy "%MEMPROCFS_PATH%\vmmdll.dll" "bin\Release\" >nul
copy "protocol.h" "bin\Release\" >nul
copy "client_python_simple.py" "bin\Release\" >nul
copy "client_易语言.e" "bin\Release\" >nul
copy "quick_test.py" "bin\Release\" >nul
copy "API_Reference.md" "bin\Release\" >nul

:: 清理临时文件
del *.obj >nul 2>&1

echo.
echo Build completed successfully!
echo.
echo Executables are in: bin\Release\
echo   - memprocfs_server.exe (Native Windows TCP Server)
echo   - client_c.exe (C Client with simplified API)
echo   - debug_client.exe (Debug client with detailed logging)
echo   - test_simple.exe (Single packet transmission test)
echo   - client_python_simple.py (Python Client with simplified API)
echo   - client_易语言.e (易语言 Client example)
echo   - quick_test.py (Multi-client test script)
echo   - API_Reference.md (Complete API documentation)
echo.
echo To run the server:
echo   cd bin\Release
echo   memprocfs_server.exe [port]
echo.
echo To test multiple VMware processes:
echo   python quick_test.py
echo.
echo To test single packet transmission:
echo   test_simple.exe [VMware进程ID]
echo.
echo Features:
echo   - Native Windows TCP server (no dependencies)
echo   - Each client connects to specific VMware process ID
echo   - Independent VMM per client (no thread locks)
echo   - Simplified client APIs
echo   - Real-time server operation logging
echo   - 易语言 client support
echo.
