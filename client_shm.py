#!/usr/bin/env python3
"""
MemProcFS Shared Memory Client (Python)

简化的共享内存客户端，支持32位和64位Python
所有复杂逻辑都在服务端处理
"""

import struct
import mmap
import time
import ctypes
from ctypes import wintypes

# Windows API
kernel32 = ctypes.windll.kernel32

# 常量定义
MEMPROCFS_SHM_NAME = "MemProcFS_SharedMemory"
MEMPROCFS_SHM_SIZE = 1024 * 1024  # 1MB
MEMPROCFS_MUTEX_NAME = "MemProcFS_Mutex"
MEMPROCFS_REQUEST_EVENT = "MemProcFS_Request"
MEMPROCFS_RESPONSE_EVENT = "MemProcFS_Response"

PACKET_MAGIC = 0x4D505346  # "MPSF"
MEMPROCFS_PROTOCOL_VERSION = 2

# 命令类型
CMD_INIT = 0x00
CMD_GET_PROCESS_ID = 0x01
CMD_GET_MODULE_BASE = 0x02
CMD_READ_MEMORY = 0x03
CMD_WRITE_MEMORY = 0x04
CMD_SHUTDOWN = 0xFF

# 状态码
STATUS_SUCCESS = 0
STATUS_ERROR = 1
STATUS_NOT_FOUND = 2
STATUS_ACCESS_DENIED = 3
STATUS_INVALID_PARAM = 4
STATUS_NOT_INITIALIZED = 5

# Windows常量
GENERIC_READ = 0x80000000
GENERIC_WRITE = 0x40000000
FILE_MAP_ALL_ACCESS = 0x001F
MUTEX_ALL_ACCESS = 0x1F0001
EVENT_ALL_ACCESS = 0x1F0003
WAIT_OBJECT_0 = 0
INFINITE = 0xFFFFFFFF

class MemProcFSClient:
    def __init__(self, vmware_process_id):
        self.vmware_process_id = vmware_process_id
        self.h_shared_memory = None
        self.h_mutex = None
        self.h_request_event = None
        self.h_response_event = None
        self.shared_memory = None
        self.sequence = 1
        
    def connect(self):
        """连接到共享内存服务器"""
        try:
            # 打开共享内存
            self.h_shared_memory = kernel32.OpenFileMappingW(
                FILE_MAP_ALL_ACCESS, False, MEMPROCFS_SHM_NAME)
            if not self.h_shared_memory:
                print("Failed to open shared memory. Is server running?")
                return False
            
            # 映射共享内存
            self.shared_memory = mmap.mmap(-1, MEMPROCFS_SHM_SIZE, 
                                         tagname=MEMPROCFS_SHM_NAME, 
                                         access=mmap.ACCESS_WRITE)
            
            # 打开同步对象
            self.h_mutex = kernel32.OpenMutexW(MUTEX_ALL_ACCESS, False, MEMPROCFS_MUTEX_NAME)
            self.h_request_event = kernel32.OpenEventW(EVENT_ALL_ACCESS, False, MEMPROCFS_REQUEST_EVENT)
            self.h_response_event = kernel32.OpenEventW(EVENT_ALL_ACCESS, False, MEMPROCFS_RESPONSE_EVENT)
            
            if not all([self.h_mutex, self.h_request_event, self.h_response_event]):
                print("Failed to open synchronization objects")
                self.disconnect()
                return False
            
            # 发送初始化请求
            if not self._send_init_request():
                print(f"Failed to initialize VMware process {self.vmware_process_id}")
                self.disconnect()
                return False
            
            print(f"Successfully connected with VMware process {self.vmware_process_id}")
            return True
            
        except Exception as e:
            print(f"Connection failed: {e}")
            self.disconnect()
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.h_response_event:
            kernel32.CloseHandle(self.h_response_event)
            self.h_response_event = None
            
        if self.h_request_event:
            kernel32.CloseHandle(self.h_request_event)
            self.h_request_event = None
            
        if self.h_mutex:
            kernel32.CloseHandle(self.h_mutex)
            self.h_mutex = None
            
        if self.shared_memory:
            self.shared_memory.close()
            self.shared_memory = None
            
        if self.h_shared_memory:
            kernel32.CloseHandle(self.h_shared_memory)
            self.h_shared_memory = None
    
    def _send_init_request(self):
        """发送初始化请求"""
        # 获取互斥锁
        kernel32.WaitForSingleObject(self.h_mutex, INFINITE)
        
        try:
            # 构造请求头
            request_header = struct.pack('<IBBHII', 
                                       PACKET_MAGIC,
                                       MEMPROCFS_PROTOCOL_VERSION,
                                       CMD_INIT,
                                       0,  # reserved
                                       4,  # data_size
                                       self.sequence)
            self.sequence += 1
            
            # 构造请求数据
            request_data = struct.pack('<I', self.vmware_process_id)
            
            # 写入共享内存
            self.shared_memory.seek(0)
            self.shared_memory.write(request_header)
            self.shared_memory.write(request_data)
            
            # 发送请求事件
            kernel32.SetEvent(self.h_request_event)
            
        finally:
            # 释放互斥锁
            kernel32.ReleaseMutex(self.h_mutex)
        
        # 等待响应
        if kernel32.WaitForSingleObject(self.h_response_event, 5000) != WAIT_OBJECT_0:
            print("Timeout waiting for init response")
            return False
        
        # 读取响应
        self.shared_memory.seek(512 * 1024)  # 响应区域
        response_header = self.shared_memory.read(12)
        magic, status, reserved1, reserved2, reserved3, data_size = struct.unpack('<IBBBBI', response_header)
        
        return status == STATUS_SUCCESS
    
    def get_process_id(self, process_name):
        """获取进程ID"""
        if not self.shared_memory:
            return 0
        
        # 获取互斥锁
        kernel32.WaitForSingleObject(self.h_mutex, INFINITE)
        
        try:
            # 构造请求头
            request_header = struct.pack('<IBBHII', 
                                       PACKET_MAGIC,
                                       MEMPROCFS_PROTOCOL_VERSION,
                                       CMD_GET_PROCESS_ID,
                                       0,  # reserved
                                       64,  # data_size
                                       self.sequence)
            self.sequence += 1
            
            # 构造请求数据
            request_data = process_name.encode('utf-8').ljust(64, b'\x00')[:64]
            
            # 写入共享内存
            self.shared_memory.seek(0)
            self.shared_memory.write(request_header)
            self.shared_memory.write(request_data)
            
            # 发送请求事件
            kernel32.SetEvent(self.h_request_event)
            
        finally:
            # 释放互斥锁
            kernel32.ReleaseMutex(self.h_mutex)
        
        # 等待响应
        if kernel32.WaitForSingleObject(self.h_response_event, 5000) != WAIT_OBJECT_0:
            print("Timeout waiting for get_process_id response")
            return 0
        
        # 读取响应
        self.shared_memory.seek(512 * 1024)  # 响应区域
        response_header = self.shared_memory.read(12)
        magic, status, reserved1, reserved2, reserved3, data_size = struct.unpack('<IBBBBI', response_header)
        
        if status != STATUS_SUCCESS:
            return 0
        
        response_data = self.shared_memory.read(4)
        process_id, = struct.unpack('<I', response_data)
        return process_id
    
    def get_module_base(self, process_id, module_name):
        """获取模块基址"""
        if not self.shared_memory:
            return 0
        
        # 获取互斥锁
        kernel32.WaitForSingleObject(self.h_mutex, INFINITE)
        
        try:
            # 构造请求头
            request_header = struct.pack('<IBBHII', 
                                       PACKET_MAGIC,
                                       MEMPROCFS_PROTOCOL_VERSION,
                                       CMD_GET_MODULE_BASE,
                                       0,  # reserved
                                       68,  # data_size (4 + 64)
                                       self.sequence)
            self.sequence += 1
            
            # 构造请求数据
            request_data = struct.pack('<I', process_id)
            request_data += module_name.encode('utf-8').ljust(64, b'\x00')[:64]
            
            # 写入共享内存
            self.shared_memory.seek(0)
            self.shared_memory.write(request_header)
            self.shared_memory.write(request_data)
            
            # 发送请求事件
            kernel32.SetEvent(self.h_request_event)
            
        finally:
            # 释放互斥锁
            kernel32.ReleaseMutex(self.h_mutex)
        
        # 等待响应
        if kernel32.WaitForSingleObject(self.h_response_event, 5000) != WAIT_OBJECT_0:
            print("Timeout waiting for get_module_base response")
            return 0
        
        # 读取响应
        self.shared_memory.seek(512 * 1024)  # 响应区域
        response_header = self.shared_memory.read(12)
        magic, status, reserved1, reserved2, reserved3, data_size = struct.unpack('<IBBBBI', response_header)
        
        if status != STATUS_SUCCESS:
            return 0
        
        response_data = self.shared_memory.read(8)
        base_address, = struct.unpack('<Q', response_data)
        return base_address
    
    def read_memory(self, process_id, address, size):
        """读取内存"""
        if not self.shared_memory or size == 0:
            return None
        
        # 获取互斥锁
        kernel32.WaitForSingleObject(self.h_mutex, INFINITE)
        
        try:
            # 构造请求头
            request_header = struct.pack('<IBBHII', 
                                       PACKET_MAGIC,
                                       MEMPROCFS_PROTOCOL_VERSION,
                                       CMD_READ_MEMORY,
                                       0,  # reserved
                                       16,  # data_size (4 + 8 + 4)
                                       self.sequence)
            self.sequence += 1
            
            # 构造请求数据
            request_data = struct.pack('<IQI', process_id, address, size)
            
            # 写入共享内存
            self.shared_memory.seek(0)
            self.shared_memory.write(request_header)
            self.shared_memory.write(request_data)
            
            # 发送请求事件
            kernel32.SetEvent(self.h_request_event)
            
        finally:
            # 释放互斥锁
            kernel32.ReleaseMutex(self.h_mutex)
        
        # 等待响应
        if kernel32.WaitForSingleObject(self.h_response_event, 5000) != WAIT_OBJECT_0:
            print("Timeout waiting for read_memory response")
            return None
        
        # 读取响应
        self.shared_memory.seek(512 * 1024)  # 响应区域
        response_header = self.shared_memory.read(12)
        magic, status, reserved1, reserved2, reserved3, data_size = struct.unpack('<IBBBBI', response_header)
        
        if status != STATUS_SUCCESS:
            return None
        
        # 读取内存响应头
        memory_response = self.shared_memory.read(4)
        bytes_processed, = struct.unpack('<I', memory_response)
        
        # 读取实际数据
        data = self.shared_memory.read(bytes_processed)
        return data
    
    def write_memory(self, process_id, address, data):
        """写入内存"""
        if not self.shared_memory or not data:
            return 0
        
        size = len(data)
        
        # 获取互斥锁
        kernel32.WaitForSingleObject(self.h_mutex, INFINITE)
        
        try:
            # 构造请求头
            request_header = struct.pack('<IBBHII', 
                                       PACKET_MAGIC,
                                       MEMPROCFS_PROTOCOL_VERSION,
                                       CMD_WRITE_MEMORY,
                                       0,  # reserved
                                       16 + size,  # data_size (4 + 8 + 4 + data)
                                       self.sequence)
            self.sequence += 1
            
            # 构造请求数据
            request_data = struct.pack('<IQI', process_id, address, size)
            request_data += data
            
            # 写入共享内存
            self.shared_memory.seek(0)
            self.shared_memory.write(request_header)
            self.shared_memory.write(request_data)
            
            # 发送请求事件
            kernel32.SetEvent(self.h_request_event)
            
        finally:
            # 释放互斥锁
            kernel32.ReleaseMutex(self.h_mutex)
        
        # 等待响应
        if kernel32.WaitForSingleObject(self.h_response_event, 5000) != WAIT_OBJECT_0:
            print("Timeout waiting for write_memory response")
            return 0
        
        # 读取响应
        self.shared_memory.seek(512 * 1024)  # 响应区域
        response_header = self.shared_memory.read(12)
        magic, status, reserved1, reserved2, reserved3, data_size = struct.unpack('<IBBBBI', response_header)
        
        if status != STATUS_SUCCESS:
            return 0
        
        # 读取内存响应头
        memory_response = self.shared_memory.read(4)
        bytes_processed, = struct.unpack('<I', memory_response)
        return bytes_processed

def main():
    """示例程序"""
    print("MemProcFS Shared Memory Client (Python)")
    print("=======================================\n")
    
    # 初始化客户端
    vmware_pid = 12345
    client = MemProcFSClient(vmware_pid)
    
    print(f"Connecting with VMware process ID: {vmware_pid}")
    if not client.connect():
        print("Failed to connect to server")
        return
    
    try:
        # 获取进程ID
        print("\nLooking for notepad.exe...")
        pid = client.get_process_id("notepad.exe")
        if pid > 0:
            print(f"Found notepad.exe with PID: {pid}")
            
            # 获取模块基址
            print("Getting ntdll.dll base address...")
            base = client.get_module_base(pid, "ntdll.dll")
            if base > 0:
                print(f"ntdll.dll base address: 0x{base:X}")
                
                # 读取内存
                print("Reading 64 bytes from memory...")
                data = client.read_memory(pid, base, 64)
                if data:
                    print(f"Successfully read {len(data)} bytes")
                    print("First 16 bytes:", " ".join(f"{b:02X}" for b in data[:16]))
                else:
                    print("Failed to read memory")
            else:
                print("ntdll.dll not found")
        else:
            print("notepad.exe not found (make sure it's running in the VM)")
    
    finally:
        client.disconnect()
    
    print("\nClient example completed.")

if __name__ == "__main__":
    main()
