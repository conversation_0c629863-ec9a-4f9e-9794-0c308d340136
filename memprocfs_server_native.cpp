/*
 * MemProcFS TCP Server v2.0 - Windows Native Implementation
 *
 * 原生Windows TCP服务器实现，支持多VMware进程并发访问
 * 无需第三方库依赖，使用Windows原生Socket API
 */

#include <iostream>
#include <thread>
#include <vector>
#include <string>
#include <sstream>
#include <mutex>
#include <memory>

// Windows includes
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>

// MemProcFS includes
#include "vmmdll.h"
#include "protocol.h"

#pragma comment(lib, "ws2_32.lib")

// 客户端连接处理类
class ClientHandler {
private:
    SOCKET m_socket;
    VMM_HANDLE m_hVMM;
    uint32_t m_clientProcessId;
    std::string m_clientAddress;
    uint32_t m_sequence;

    // 响应缓冲区，避免频繁内存分配
    static const uint32_t MAX_RESPONSE_SIZE = 64 * 1024;  // 64KB
    uint8_t m_responseBuffer[MAX_RESPONSE_SIZE];

public:
    ClientHandler(SOCKET socket, const std::string& clientAddress) 
        : m_socket(socket), m_hVMM(nullptr), m_clientProcessId(0), 
          m_clientAddress(clientAddress), m_sequence(1) {
    }

    ~ClientHandler() {
        if (m_hVMM) {
            VMMDLL_Close(m_hVMM);
        }
        if (m_socket != INVALID_SOCKET) {
            closesocket(m_socket);
        }
    }

    // 处理客户端连接
    void HandleClient() {
        std::cout << "[" << m_clientAddress << "] Client connected" << std::endl;

        try {
            while (true) {
                // 接收数据包头
                packet_header_t header;
                if (!ReceiveData(&header, sizeof(header))) {
                    break;
                }

                // 验证数据包头
                if (!ValidatePacketHeader(&header)) {
                    std::cout << "[" << m_clientAddress << "] Invalid packet header" << std::endl;
                    break;
                }

                // 接收数据包内容
                std::vector<uint8_t> data(header.data_size);
                if (header.data_size > 0) {
                    if (!ReceiveData(data.data(), header.data_size)) {
                        break;
                    }
                }

                // 处理命令
                HandleCommand(header, data);
            }
        }
        catch (const std::exception& e) {
            std::cout << "[" << m_clientAddress << "] Exception: " << e.what() << std::endl;
        }

        std::cout << "[" << m_clientAddress << "] Client disconnected" << std::endl;
    }

private:
    // 发送数据
    bool SendData(const void* data, uint32_t size) {
        const uint8_t* bytes = static_cast<const uint8_t*>(data);
        uint32_t sent = 0;

        while (sent < size) {
            int result = send(m_socket, reinterpret_cast<const char*>(bytes + sent), size - sent, 0);
            if (result == SOCKET_ERROR) {
                return false;
            }
            sent += result;
        }
        return true;
    }

    // 接收数据
    bool ReceiveData(void* data, uint32_t size) {
        uint8_t* bytes = static_cast<uint8_t*>(data);
        uint32_t received = 0;

        while (received < size) {
            int result = recv(m_socket, reinterpret_cast<char*>(bytes + received), size - received, 0);
            if (result <= 0) {
                return false;
            }
            received += result;
        }
        return true;
    }

    // 验证数据包头
    bool ValidatePacketHeader(const packet_header_t* header) {
        return header->magic == PACKET_MAGIC && header->version == MEMPROCFS_PROTOCOL_VERSION;
    }

    // 发送响应
    void SendResponse(uint32_t sequence, status_code_t status, const void* data, uint32_t dataSize) {
        // 计算总的响应数据大小
        uint32_t responseDataSize = sizeof(response_header_t) + dataSize;
        uint32_t totalSize = sizeof(packet_header_t) + responseDataSize;

        // 检查缓冲区大小是否足够
        if (totalSize > MAX_RESPONSE_SIZE) {
            std::cerr << "[" << m_clientAddress << "] Response too large: " << totalSize << " bytes (max: " << MAX_RESPONSE_SIZE << " bytes)" << std::endl;

            // 直接构造一个简单的错误响应，避免递归调用
            uint8_t errorBuffer[sizeof(packet_header_t) + sizeof(response_header_t)];

            packet_header_t* errorHeader = reinterpret_cast<packet_header_t*>(errorBuffer);
            errorHeader->magic = PACKET_MAGIC;
            errorHeader->version = MEMPROCFS_PROTOCOL_VERSION;
            errorHeader->command = CMD_RESPONSE;
            errorHeader->flags = 0;
            errorHeader->data_size = sizeof(response_header_t);
            errorHeader->sequence = sequence;

            response_header_t* errorDataHeader = reinterpret_cast<response_header_t*>(errorBuffer + sizeof(packet_header_t));
            errorDataHeader->status = STATUS_ERROR_INTERNAL;
            errorDataHeader->reserved[0] = 0;
            errorDataHeader->reserved[1] = 0;
            errorDataHeader->reserved[2] = 0;
            errorDataHeader->data_size = 0;

            SendData(errorBuffer, sizeof(errorBuffer));
            return;
        }

        // 使用预分配的缓冲区
        uint8_t* buffer = m_responseBuffer;

        // 构造数据包头
        packet_header_t* responseHeader = reinterpret_cast<packet_header_t*>(buffer);
        responseHeader->magic = PACKET_MAGIC;
        responseHeader->version = MEMPROCFS_PROTOCOL_VERSION;
        responseHeader->command = CMD_RESPONSE;
        responseHeader->flags = 0;
        responseHeader->data_size = responseDataSize;
        responseHeader->sequence = sequence;

        // 构造响应数据头
        response_header_t* responseDataHeader = reinterpret_cast<response_header_t*>(buffer + sizeof(packet_header_t));
        responseDataHeader->status = status;
        responseDataHeader->reserved[0] = 0;
        responseDataHeader->reserved[1] = 0;
        responseDataHeader->reserved[2] = 0;
        responseDataHeader->data_size = dataSize;

        // 复制数据（如果有）
        if (data && dataSize > 0) {
            memcpy(buffer + sizeof(packet_header_t) + sizeof(response_header_t), data, dataSize);
        }

        // 一次性发送整个数据包
        if (status != STATUS_SUCCESS) {
            std::cout << "[" << m_clientAddress << "] Sending error response: status=" << (int)status << ", size=" << totalSize << " bytes" << std::endl;
        }
        SendData(buffer, totalSize);
    }

    // 发送错误响应
    void SendErrorResponse(uint32_t sequence, status_code_t status) {
        // 直接构造错误响应，避免递归调用
        uint32_t responseDataSize = sizeof(response_header_t);
        uint32_t totalSize = sizeof(packet_header_t) + responseDataSize;

        uint8_t* buffer = m_responseBuffer;

        // 构造数据包头
        packet_header_t* responseHeader = reinterpret_cast<packet_header_t*>(buffer);
        responseHeader->magic = PACKET_MAGIC;
        responseHeader->version = MEMPROCFS_PROTOCOL_VERSION;
        responseHeader->command = CMD_RESPONSE;
        responseHeader->flags = 0;
        responseHeader->data_size = responseDataSize;
        responseHeader->sequence = sequence;

        // 构造响应数据头
        response_header_t* responseDataHeader = reinterpret_cast<response_header_t*>(buffer + sizeof(packet_header_t));
        responseDataHeader->status = status;
        responseDataHeader->reserved[0] = 0;
        responseDataHeader->reserved[1] = 0;
        responseDataHeader->reserved[2] = 0;
        responseDataHeader->data_size = 0;

        std::cout << "[" << m_clientAddress << "] Sending error response: status=" << (int)status << ", size=" << totalSize << " bytes" << std::endl;
        SendData(buffer, totalSize);
    }

    // 处理命令
    void HandleCommand(const packet_header_t& header, const std::vector<uint8_t>& data) {
        switch (header.command) {
            case CMD_INIT:
                HandleInit(header, data);
                break;
            case CMD_PING:
                HandlePing(header);
                break;
            case CMD_GET_VERSION:
                HandleGetVersion(header);
                break;
            case CMD_READ_MEMORY:
                HandleReadMemory(header, data);
                break;
            case CMD_WRITE_MEMORY:
                HandleWriteMemory(header, data);
                break;
            case CMD_GET_PROCESS_BY_NAME:
                HandleGetProcessByName(header, data);
                break;
            case CMD_GET_MODULE_BASE:
                HandleGetModuleBase(header, data);
                break;
            default:
                std::cout << "[" << m_clientAddress << "] Unknown command: 0x" << std::hex << header.command << std::dec << std::endl;
                SendErrorResponse(header.sequence, STATUS_ERROR_INVALID_CMD);
                break;
        }
    }

    // 初始化VMware进程
    void HandleInit(const packet_header_t& header, const std::vector<uint8_t>& data) {
        if (data.size() < sizeof(uint32_t)) {
            SendErrorResponse(header.sequence, STATUS_ERROR_INVALID_PARAM);
            return;
        }

        m_clientProcessId = *reinterpret_cast<const uint32_t*>(data.data());
        std::cout << "[" << m_clientAddress << "] INIT: VMware process ID " << m_clientProcessId << std::endl;

        // 构造VMware设备参数
        std::string deviceArg = "vmware://ro=1,id=" + std::to_string(m_clientProcessId);

        LPCSTR argv[] = {
            "",
            "-device",
            deviceArg.c_str(),
            "-v"
        };

        m_hVMM = VMMDLL_Initialize(4, argv);
        if (!m_hVMM) {
            std::cout << "[" << m_clientAddress << "] INIT: Failed to initialize VMware process " << m_clientProcessId << std::endl;
            SendErrorResponse(header.sequence, STATUS_ERROR_INTERNAL);
            return;
        }

        std::cout << "[" << m_clientAddress << "] INIT: Successfully initialized VMware process " << m_clientProcessId << std::endl;
        SendResponse(header.sequence, STATUS_SUCCESS, nullptr, 0);
    }

    // 处理Ping
    void HandlePing(const packet_header_t& header) {
        std::cout << "[" << m_clientAddress << "] ping" << std::endl;
        SendResponse(header.sequence, STATUS_SUCCESS, nullptr, 0);
    }

    // 处理获取版本
    void HandleGetVersion(const packet_header_t& header) {
        std::cout << "[" << m_clientAddress << "] get_version" << std::endl;
        uint32_t version = MEMPROCFS_PROTOCOL_VERSION;
        SendResponse(header.sequence, STATUS_SUCCESS, &version, sizeof(version));
    }

    // 处理读取内存
    void HandleReadMemory(const packet_header_t& header, const std::vector<uint8_t>& data) {
        if (!m_hVMM) {
            std::cout << "[" << m_clientAddress << "] read_memory: Not initialized" << std::endl;
            SendErrorResponse(header.sequence, STATUS_ERROR_NOT_INITIALIZED);
            return;
        }

        if (data.size() < sizeof(memory_request_t)) {
            SendErrorResponse(header.sequence, STATUS_ERROR_INVALID_PARAM);
            return;
        }

        const memory_request_t* request = reinterpret_cast<const memory_request_t*>(data.data());
        
        std::cout << "[" << m_clientAddress << "] read_memory: PID=" << request->process_id 
                  << " Addr=0x" << std::hex << request->address << std::dec 
                  << " Size=" << request->size << std::endl;

        // 分配响应缓冲区
        std::vector<uint8_t> responseBuffer(sizeof(memory_response_t) + request->size);
        memory_response_t* response = reinterpret_cast<memory_response_t*>(responseBuffer.data());
        uint8_t* memoryData = responseBuffer.data() + sizeof(memory_response_t);

        // 读取内存
        BOOL result = VMMDLL_MemRead(m_hVMM, request->process_id, request->address, memoryData, request->size);

        if (result) {
            response->bytes_processed = request->size;
            std::cout << "[" << m_clientAddress << "] read_memory: SUCCESS - " << request->size << " bytes" << std::endl;
            SendResponse(header.sequence, STATUS_SUCCESS, responseBuffer.data(), responseBuffer.size());
        } else {
            response->bytes_processed = 0;
            std::cout << "[" << m_clientAddress << "] read_memory: FAILED" << std::endl;
            SendResponse(header.sequence, STATUS_ERROR_READ_FAILED, response, sizeof(memory_response_t));
        }
    }

    // 处理写入内存
    void HandleWriteMemory(const packet_header_t& header, const std::vector<uint8_t>& data) {
        if (!m_hVMM) {
            std::cout << "[" << m_clientAddress << "] write_memory: Not initialized" << std::endl;
            SendErrorResponse(header.sequence, STATUS_ERROR_NOT_INITIALIZED);
            return;
        }
        
        if (data.size() < sizeof(memory_request_t)) {
            SendErrorResponse(header.sequence, STATUS_ERROR_INVALID_PARAM);
            return;
        }
        
        const memory_request_t* request = reinterpret_cast<const memory_request_t*>(data.data());
        const uint8_t* writeData = data.data() + sizeof(memory_request_t);
        uint32_t writeDataSize = data.size() - sizeof(memory_request_t);
        
        if (writeDataSize < request->size) {
            SendErrorResponse(header.sequence, STATUS_ERROR_INVALID_PARAM);
            return;
        }
        
        std::cout << "[" << m_clientAddress << "] write_memory: PID=" << request->process_id 
                  << " Addr=0x" << std::hex << request->address << std::dec 
                  << " Size=" << request->size << std::endl;
        
        // 写入内存
        BOOL result = VMMDLL_MemWrite(m_hVMM, request->process_id, request->address, (PBYTE)writeData, request->size);
        
        memory_response_t response;
        response.bytes_processed = result ? request->size : 0;
        
        status_code_t status = result ? STATUS_SUCCESS : STATUS_ERROR_WRITE_FAILED;
        std::cout << "[" << m_clientAddress << "] write_memory: " << (result ? "SUCCESS" : "FAILED") << std::endl;
        SendResponse(header.sequence, status, &response, sizeof(response));
    }

    // 处理获取进程ID
    void HandleGetProcessByName(const packet_header_t& header, const std::vector<uint8_t>& data) {
        if (!m_hVMM) {
            std::cout << "[" << m_clientAddress << "] get_process_by_name: Not initialized" << std::endl;
            SendErrorResponse(header.sequence, STATUS_ERROR_NOT_INITIALIZED);
            return;
        }

        if (data.size() < sizeof(process_by_name_request_t)) {
            SendErrorResponse(header.sequence, STATUS_ERROR_INVALID_PARAM);
            return;
        }

        const process_by_name_request_t* request = reinterpret_cast<const process_by_name_request_t*>(data.data());

        std::cout << "[" << m_clientAddress << "] get_process_by_name: " << request->process_name << std::endl;

        DWORD dwPID = 0;
        BOOL result = VMMDLL_PidGetFromName(m_hVMM, (LPSTR)request->process_name, &dwPID);

        if (result) {
            process_by_name_response_t response;
            response.process_id = dwPID;
            std::cout << "[" << m_clientAddress << "] get_process_by_name: SUCCESS - PID=" << dwPID << std::endl;
            SendResponse(header.sequence, STATUS_SUCCESS, &response, sizeof(response));
        } else {
            std::cout << "[" << m_clientAddress << "] get_process_by_name: NOT FOUND" << std::endl;
            SendErrorResponse(header.sequence, STATUS_ERROR_PROCESS_NOT_FOUND);
        }
    }

    // 处理获取模块基址
    void HandleGetModuleBase(const packet_header_t& header, const std::vector<uint8_t>& data) {
        if (!m_hVMM) {
            std::cout << "[" << m_clientAddress << "] get_module_base: Not initialized" << std::endl;
            SendErrorResponse(header.sequence, STATUS_ERROR_NOT_INITIALIZED);
            return;
        }

        if (data.size() < sizeof(module_request_t)) {
            SendErrorResponse(header.sequence, STATUS_ERROR_INVALID_PARAM);
            return;
        }

        const module_request_t* request = reinterpret_cast<const module_request_t*>(data.data());

        std::cout << "[" << m_clientAddress << "] get_module_base: PID=" << request->process_id
                  << " Module=" << request->module_name << std::endl;

        ULONG64 vaBase = VMMDLL_ProcessGetModuleBaseU(m_hVMM, request->process_id, (LPSTR)request->module_name);

        if (vaBase != 0) {
            module_base_response_t response;
            response.base_address = vaBase;
            std::cout << "[" << m_clientAddress << "] get_module_base: SUCCESS - Base=0x"
                      << std::hex << vaBase << std::dec << std::endl;
            SendResponse(header.sequence, STATUS_SUCCESS, &response, sizeof(response));
        } else {
            std::cout << "[" << m_clientAddress << "] get_module_base: NOT FOUND" << std::endl;
            SendErrorResponse(header.sequence, STATUS_ERROR_MODULE_NOT_FOUND);
        }
    }
};

// TCP服务器类
class MemProcFSServer {
private:
    uint16_t m_port;
    SOCKET m_serverSocket;
    bool m_running;
    std::vector<std::thread> m_clientThreads;

public:
    MemProcFSServer(uint16_t port = MEMPROCFS_DEFAULT_PORT)
        : m_port(port), m_serverSocket(INVALID_SOCKET), m_running(false) {
    }

    ~MemProcFSServer() {
        Stop();
    }

    // 初始化服务器
    bool Initialize() {
        // 初始化Winsock
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            std::cerr << "WSAStartup failed" << std::endl;
            return false;
        }

        // 创建服务器socket
        m_serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (m_serverSocket == INVALID_SOCKET) {
            std::cerr << "Failed to create server socket" << std::endl;
            WSACleanup();
            return false;
        }

        // 设置socket选项
        int opt = 1;
        setsockopt(m_serverSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));

        // 绑定地址
        sockaddr_in serverAddr;
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_addr.s_addr = INADDR_ANY;
        serverAddr.sin_port = htons(m_port);

        if (bind(m_serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            std::cerr << "Failed to bind server socket to port " << m_port << std::endl;
            closesocket(m_serverSocket);
            WSACleanup();
            return false;
        }

        // 开始监听
        if (listen(m_serverSocket, SOMAXCONN) == SOCKET_ERROR) {
            std::cerr << "Failed to listen on server socket" << std::endl;
            closesocket(m_serverSocket);
            WSACleanup();
            return false;
        }

        std::cout << "MemProcFS TCP Server v2.0 - Native Windows" << std::endl;
        std::cout << "Multi-VMware Process Support" << std::endl;
        std::cout << "Listening on port: " << m_port << std::endl;
        std::cout << "Each client will initialize with specific VMware process ID" << std::endl;
        std::cout << "Press Ctrl+C to stop the server" << std::endl;

        return true;
    }

    // 启动服务器
    void Start() {
        if (!Initialize()) {
            return;
        }

        m_running = true;

        while (m_running) {
            sockaddr_in clientAddr;
            int clientAddrLen = sizeof(clientAddr);

            SOCKET clientSocket = accept(m_serverSocket, (sockaddr*)&clientAddr, &clientAddrLen);
            if (clientSocket == INVALID_SOCKET) {
                if (m_running) {
                    std::cerr << "Failed to accept client connection" << std::endl;
                }
                continue;
            }

            // 获取客户端地址
            char clientIP[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
            std::string clientAddress = std::string(clientIP) + ":" + std::to_string(ntohs(clientAddr.sin_port));

            // 创建客户端处理线程
            m_clientThreads.emplace_back([this, clientSocket, clientAddress]() {
                auto handler = std::make_unique<ClientHandler>(clientSocket, clientAddress);
                handler->HandleClient();
            });
        }
    }

    // 停止服务器
    void Stop() {
        m_running = false;

        if (m_serverSocket != INVALID_SOCKET) {
            closesocket(m_serverSocket);
            m_serverSocket = INVALID_SOCKET;
        }

        // 等待所有客户端线程结束
        for (auto& thread : m_clientThreads) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        m_clientThreads.clear();

        WSACleanup();
        std::cout << "Server stopped." << std::endl;
    }
};

// 全局服务器实例
std::unique_ptr<MemProcFSServer> g_server;

// Ctrl+C处理函数
BOOL WINAPI ConsoleCtrlHandler(DWORD dwCtrlType) {
    switch (dwCtrlType) {
        case CTRL_C_EVENT:
        case CTRL_BREAK_EVENT:
        case CTRL_CLOSE_EVENT:
            std::cout << "\nShutting down server..." << std::endl;
            if (g_server) {
                g_server->Stop();
            }
            return TRUE;
        default:
            return FALSE;
    }
}

// 主函数
int main(int argc, char* argv[]) {
    uint16_t port = MEMPROCFS_DEFAULT_PORT;

    // 解析命令行参数
    if (argc > 1) {
        port = static_cast<uint16_t>(atoi(argv[1]));
        if (port == 0) {
            std::cerr << "Invalid port number: " << argv[1] << std::endl;
            return 1;
        }
    }

    // 设置Ctrl+C处理函数
    SetConsoleCtrlHandler(ConsoleCtrlHandler, TRUE);

    // 创建并启动服务器
    g_server = std::make_unique<MemProcFSServer>(port);
    g_server->Start();

    return 0;
}
