# MemProcFS + PCILeech 代码执行指南

本指南详细说明如何在VMware虚拟机的指定进程中执行代码，结合PCILeech和MemProcFS的功能。

## 📋 功能对比

### PCILeech 核心功能
| 功能 | 描述 | 适用场景 |
|------|------|----------|
| **内核模块注入** | 在目标内核中注入并执行代码 | 内核级操作、驱动绕过 |
| **Shellcode执行** | 执行自定义机器码 | 灵活的代码执行 |
| **内存DMA访问** | 直接内存访问，绕过OS保护 | 高权限内存操作 |
| **进程注入** | 向用户态进程注入代码 | 用户态Hook、API拦截 |

### MemProcFS 核心功能
| 功能 | 描述 | 适用场景 |
|------|------|----------|
| **内存读写** | 读写虚拟机进程内存 | 数据获取、内存修改 |
| **进程枚举** | 获取进程列表和信息 | 目标定位 |
| **模块分析** | 获取模块基址和信息 | 地址计算、API定位 |
| **文件系统访问** | 访问虚拟机文件系统 | 文件操作、配置修改 |

## 🎯 代码执行方法

### 1. Shellcode注入执行

**原理**: 将机器码写入目标进程内存并执行

```c
// 示例：简单的MessageBox shellcode
uint8_t msgbox_shellcode[] = {
    0x48, 0x83, 0xEC, 0x28,                         // sub rsp, 0x28
    0x48, 0x31, 0xC9,                               // xor rcx, rcx
    0x48, 0x31, 0xD2,                               // xor rdx, rdx
    0x49, 0xB8, 0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x00, // mov r8, "Hello"
    0x49, 0xB9, 0x57, 0x6F, 0x72, 0x6C, 0x64, 0x00, // mov r9, "World"
    0xFF, 0x15, 0x02, 0x00, 0x00, 0x00,             // call [MessageBoxA]
    0x48, 0x83, 0xC4, 0x28,                         // add rsp, 0x28
    0xC3                                            // ret
};

// 执行shellcode
uint64_t thread_handle, shellcode_addr;
uint32_t thread_id;

int result = memprocfs_execute_shellcode(
    &client, 
    target_pid, 
    msgbox_shellcode, 
    sizeof(msgbox_shellcode),
    0,  // 执行方法：CreateRemoteThread
    &thread_handle,
    &shellcode_addr,
    &thread_id
);
### 2. 函数调用执行

**原理**: 直接调用目标进程中的现有函数

```c
// 获取kernel32.dll基址
uint64_t kernel32_base = memprocfs_get_module_base(&client, target_pid, "kernel32.dll");

// 计算GetCurrentProcessId函数地址
uint64_t getCurrentProcessId = kernel32_base + 0x12345; // 实际偏移需要计算

// 调用函数
uint64_t return_value;
uint32_t last_error;

int result = memprocfs_call_function(
    &client,
    target_pid,
    getCurrentProcessId,
    0, 0, 0, 0,  // 参数
    0,           // 参数数量
    &return_value,
    &last_error
);

printf("Process ID: %llu\n", return_value);
```

### 3. DLL注入执行

**原理**: 将DLL注入到目标进程并执行其代码

```c
// 注入DLL
uint64_t module_handle, module_base;

int result = memprocfs_inject_dll(
    &client,
    target_pid,
    "C:\\MyTools\\payload.dll",
    0,  // 注入方法：LoadLibrary
    &module_handle,
    &module_base
);

if (result) {
    printf("DLL injected at: 0x%llX\n", module_base);
    
    // 调用DLL中的导出函数
    uint64_t export_func = module_base + 0x1000; // DLL导出函数偏移
    memprocfs_call_function(&client, target_pid, export_func, 0, 0, 0, 0, 0, NULL, NULL);
}
```

## 🔧 高级技术

### 1. ROP链构造

**用途**: 绕过DEP/NX保护

```c
// ROP链示例（x64）
uint64_t rop_chain[] = {
    0x140001000,  // pop rcx; ret
    0x00000001,   // rcx = 1
    0x140001010,  // pop rdx; ret  
    0x00000002,   // rdx = 2
    0x140001020,  // target function address
};

// 将ROP链写入栈
memprocfs_write_memory(&client, target_pid, stack_addr, rop_chain, sizeof(rop_chain));
```

### 2. API Hook技术

**用途**: 拦截和修改API调用

```c
// Hook MessageBoxA
uint64_t messageBoxA = GetProcAddress("user32.dll", "MessageBoxA");

// 保存原始字节
uint8_t original_bytes[5];
memprocfs_read_memory(&client, target_pid, messageBoxA, original_bytes, 5);

// 写入跳转指令
uint8_t hook_bytes[] = {0xE9, 0x00, 0x00, 0x00, 0x00}; // jmp rel32
uint32_t offset = (uint32_t)(hook_function - messageBoxA - 5);
memcpy(hook_bytes + 1, &offset, 4);

memprocfs_write_memory(&client, target_pid, messageBoxA, hook_bytes, 5);
```

### 3. 内存补丁技术

**用途**: 修改程序逻辑

```c
// 将条件跳转改为无条件跳转
uint64_t patch_addr = 0x140001234;
uint8_t patch_bytes[] = {0xEB}; // jmp short (原来是je)

memprocfs_write_memory(&client, target_pid, patch_addr, patch_bytes, 1);
```

## 🛡️ 绕过保护机制

### 1. ASLR绕过

```c
// 通过模块基址计算实际地址
uint64_t ntdll_base = memprocfs_get_module_base(&client, target_pid, "ntdll.dll");
uint64_t target_func = ntdll_base + known_offset;
```

### 2. DEP/NX绕过

```c
// 使用ROP链或现有可执行内存
// 查找可执行内存区域
uint64_t executable_region = FindExecutableMemory(&client, target_pid);
```

### 3. CFG绕过

```c
// 使用间接调用或现有函数指针
uint64_t function_pointer = ReadFunctionPointer(&client, target_pid);
memprocfs_call_function(&client, target_pid, function_pointer, ...);
```

## 📝 实际应用示例

### 示例1：游戏内存修改

```c
// 修改游戏中的生命值
uint32_t game_pid = memprocfs_get_process_id(&client, "game.exe");
uint64_t game_base = memprocfs_get_module_base(&client, game_pid, "game.exe");

// 生命值地址 = 基址 + 偏移
uint64_t health_addr = game_base + 0x12345;
uint32_t new_health = 9999;

memprocfs_write_memory(&client, game_pid, health_addr, &new_health, sizeof(new_health));
```

### 示例2：进程监控

```c
// 注入监控DLL
memprocfs_inject_dll(&client, target_pid, "monitor.dll", 0, NULL, NULL);

// 监控DLL会Hook关键API并记录行为
```

### 示例3：自动化操作

```c
// 模拟鼠标点击
uint64_t user32_base = memprocfs_get_module_base(&client, target_pid, "user32.dll");
uint64_t setcursorpos = user32_base + 0x1234;

// 调用SetCursorPos(100, 200)
memprocfs_call_function(&client, target_pid, setcursorpos, 100, 200, 0, 0, 2, NULL, NULL);
```

## ⚠️ 注意事项

### 安全考虑
1. **仅在授权环境中使用**
2. **遵守相关法律法规**
3. **不要用于恶意目的**

### 技术限制
1. **需要管理员权限**
2. **可能被安全软件检测**
3. **依赖于目标系统架构**

### 调试建议
1. **使用调试器验证地址**
2. **检查内存保护属性**
3. **监控执行结果**

## 🔗 相关资源

- [PCILeech GitHub](https://github.com/ufrisk/pcileech)
- [MemProcFS GitHub](https://github.com/ufrisk/MemProcFS)
- [Windows API文档](https://docs.microsoft.com/en-us/windows/win32/api/)
- [x64 Assembly参考](https://www.intel.com/content/www/us/en/developer/articles/technical/intel-sdm.html)

---

**免责声明**: 本指南仅用于教育和研究目的。使用者需要确保遵守所有适用的法律法规，并仅在授权的环境中使用这些技术。
