/*
 * 测试缓冲区修复
 * 验证响应缓冲区大小是否正确
 */

#include <stdio.h>
#include "protocol.h"

int main() {
    printf("测试响应缓冲区大小计算\n");
    printf("========================\n\n");
    
    // 测试各种响应结构的大小
    printf("协议结构大小:\n");
    printf("  packet_header_t: %zu bytes\n", sizeof(packet_header_t));
    printf("  response_header_t: %zu bytes\n", sizeof(response_header_t));
    printf("  process_by_name_response_t: %zu bytes\n", sizeof(process_by_name_response_t));
    printf("  module_base_response_t: %zu bytes\n", sizeof(module_base_response_t));
    printf("  memory_response_t: %zu bytes\n", sizeof(memory_response_t));
    
    printf("\n需要的缓冲区大小:\n");
    printf("  获取进程ID: %zu bytes\n", sizeof(response_header_t) + sizeof(process_by_name_response_t));
    printf("  获取模块基址: %zu bytes\n", sizeof(response_header_t) + sizeof(module_base_response_t));
    printf("  内存操作: %zu bytes\n", sizeof(response_header_t) + sizeof(memory_response_t));
    
    printf("\n✓ 缓冲区大小计算正确!\n");
    
    return 0;
}
