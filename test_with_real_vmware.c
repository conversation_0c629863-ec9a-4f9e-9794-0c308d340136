/*
 * 使用真实VMware进程ID的测试程序
 * 自动检测VMware进程并测试单包发送功能
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
    #include <windows.h>
    #include <tlhelp32.h>
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    typedef int socklen_t;
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define closesocket close
#endif

#include "protocol.h"

// 查找VMware进程
DWORD FindVMwareProcess() {
#ifdef _WIN32
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);
    
    if (!Process32First(hSnapshot, &pe32)) {
        CloseHandle(hSnapshot);
        return 0;
    }
    
    do {
        // 查找VMware相关进程
        if (strstr(pe32.szExeFile, "vmware-vmx.exe") != NULL ||
            strstr(pe32.szExeFile, "vmware.exe") != NULL ||
            strstr(pe32.szExeFile, "vmplayer.exe") != NULL) {
            printf("找到VMware进程: %s (PID: %lu)\n", pe32.szExeFile, pe32.th32ProcessID);
            CloseHandle(hSnapshot);
            return pe32.th32ProcessID;
        }
    } while (Process32Next(hSnapshot, &pe32));
    
    CloseHandle(hSnapshot);
#endif
    return 0;
}

// 发送数据
int send_data(SOCKET socket, const void* data, uint32_t size) {
    const uint8_t* bytes = (const uint8_t*)data;
    uint32_t sent = 0;
    
    while (sent < size) {
        int result = send(socket, (const char*)(bytes + sent), size - sent, 0);
        if (result <= 0) {
            return 0;
        }
        sent += result;
    }
    return 1;
}

// 接收数据
int receive_data(SOCKET socket, void* data, uint32_t size) {
    uint8_t* bytes = (uint8_t*)data;
    uint32_t received = 0;
    
    while (received < size) {
        int result = recv(socket, (char*)(bytes + received), size - received, 0);
        if (result <= 0) {
            return 0;
        }
        received += result;
    }
    return 1;
}

// 测试初始化
int test_init_with_vmware_pid(SOCKET socket, DWORD vmware_pid) {
    printf("\n测试初始化VMware进程 %lu...\n", vmware_pid);
    
    // 发送初始化命令
    packet_header_t header;
    header.magic = PACKET_MAGIC;
    header.version = MEMPROCFS_PROTOCOL_VERSION;
    header.command = CMD_INIT;
    header.flags = 0;
    header.data_size = sizeof(uint32_t);
    header.sequence = 1;
    
    if (!send_data(socket, &header, sizeof(header))) {
        printf("✗ 发送初始化头失败\n");
        return 0;
    }
    
    if (!send_data(socket, &vmware_pid, sizeof(vmware_pid))) {
        printf("✗ 发送VMware进程ID失败\n");
        return 0;
    }
    
    // 接收响应
    packet_header_t response_header;
    if (!receive_data(socket, &response_header, sizeof(response_header))) {
        printf("✗ 接收响应包头失败\n");
        return 0;
    }
    
    printf("响应包信息: Magic=0x%08X, Cmd=0x%02X, DataSize=%u, Seq=%u\n",
           response_header.magic, response_header.command, 
           response_header.data_size, response_header.sequence);
    
    if (response_header.data_size > 0) {
        uint8_t* response_data = malloc(response_header.data_size);
        if (!response_data) {
            printf("✗ 内存分配失败\n");
            return 0;
        }
        
        if (!receive_data(socket, response_data, response_header.data_size)) {
            printf("✗ 接收响应数据失败\n");
            free(response_data);
            return 0;
        }
        
        response_header_t* resp_hdr = (response_header_t*)response_data;
        
        const char* status_str = "UNKNOWN";
        switch(resp_hdr->status) {
            case STATUS_SUCCESS: status_str = "SUCCESS"; break;
            case STATUS_ERROR_INTERNAL: status_str = "INTERNAL"; break;
            default: status_str = "OTHER"; break;
        }
        
        printf("响应状态: %u (%s)\n", resp_hdr->status, status_str);

        free(response_data);

        int success = (resp_hdr->status == STATUS_SUCCESS);
        if (success) {
            printf("✓ 初始化成功！\n");
        } else {
            printf("✗ 初始化失败: %s\n", status_str);
        }
        return success;
    }
    
    printf("✗ 响应数据大小为0\n");
    return 0;
}

int main(int argc, char* argv[]) {
    printf("MemProcFS TCP服务器 - 真实VMware进程测试\n");
    printf("======================================\n");

    DWORD vmware_pid = 0;

    // 检查命令行参数
    if (argc > 1) {
        vmware_pid = (DWORD)atoi(argv[1]);
        printf("使用命令行指定的VMware进程ID: %lu\n", vmware_pid);
    } else {
        // 查找VMware进程
        printf("正在查找VMware进程...\n");
        vmware_pid = FindVMwareProcess();

        if (vmware_pid == 0) {
            printf("✗ 未找到VMware进程\n");
            printf("请确保VMware Workstation或VMware Player正在运行\n");
            printf("支持的进程名: vmware-vmx.exe, vmware.exe, vmplayer.exe\n");
            printf("\n您可以手动指定VMware进程ID:\n");
            printf("  test_with_real_vmware.exe <进程ID>\n");

            // 提示用户输入进程ID
            printf("\n请输入VMware进程ID (或按Enter退出): ");
            char input[32] = {0};
            if (fgets(input, sizeof(input), stdin) != NULL) {
                // 移除换行符
                char* newline = strchr(input, '\n');
                if (newline) *newline = 0;

                // 如果用户输入了进程ID
                if (input[0] != '\0') {
                    vmware_pid = (DWORD)atoi(input);
                    printf("使用手动输入的VMware进程ID: %lu\n", vmware_pid);
                }
            }

            if (vmware_pid == 0) {
                return 1;
            }
        }
    }
    
#ifdef _WIN32
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        printf("WSAStartup失败\n");
        return 1;
    }
#endif

    SOCKET client_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (client_socket == INVALID_SOCKET) {
        printf("创建socket失败\n");
#ifdef _WIN32
        WSACleanup();
#endif
        return 1;
    }

    struct sockaddr_in server_addr;
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(12345);
    inet_pton(AF_INET, "127.0.0.1", &server_addr.sin_addr);

    printf("\n连接到服务器...\n");
    if (connect(client_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        printf("✗ 连接失败\n");
        printf("请确保MemProcFS服务器正在运行在端口12345\n");
        closesocket(client_socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 1;
    }

    printf("✓ 连接成功\n");
    
    // 测试初始化
    int result = test_init_with_vmware_pid(client_socket, vmware_pid);
    
    closesocket(client_socket);
#ifdef _WIN32
    WSACleanup();
#endif
    
    if (result) {
        printf("\n✓ 测试成功！\n");
        printf("服务器成功初始化了VMware进程，并使用单包发送响应。\n");
    } else {
        printf("\n✗ 测试失败！\n");
        printf("可能的原因:\n");
        printf("1. VMware进程ID无效\n");
        printf("2. MemProcFS无法访问该VMware进程\n");
        printf("3. 需要管理员权限\n");
    }
    
    return result ? 0 : 1;
}
