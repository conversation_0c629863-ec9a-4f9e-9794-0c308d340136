/*
 * 测试编译修复
 * 验证VMMDLL_Initialize参数类型和PROTOCOL_VERSION定义
 */

#include <stdio.h>
#include "protocol.h"

// 模拟VMMDLL_Initialize函数签名
typedef void* VMM_HANDLE;
VMM_HANDLE VMMDLL_Initialize(DWORD argc, LPCSTR argv[]);

// 模拟实现
VMM_HANDLE VMMDLL_Initialize(DWORD argc, LPCSTR argv[]) {
    printf("VMMDLL_Initialize called with %lu arguments:\n", argc);
    for (DWORD i = 0; i < argc; i++) {
        printf("  argv[%lu] = \"%s\"\n", i, argv[i]);
    }
    return (VMM_HANDLE)0x12345678;  // 模拟句柄
}

int main() {
    printf("测试编译修复\n");
    printf("============\n\n");
    
    // 测试PROTOCOL_VERSION定义
    printf("协议版本定义:\n");
    printf("  MEMPROCFS_PROTOCOL_VERSION = %d\n", MEMPROCFS_PROTOCOL_VERSION);
    printf("  PROTOCOL_VERSION = %d\n", PROTOCOL_VERSION);
    
    if (PROTOCOL_VERSION == MEMPROCFS_PROTOCOL_VERSION) {
        printf("✓ 版本定义一致\n");
    } else {
        printf("✗ 版本定义不一致\n");
        return 1;
    }
    
    // 测试VMMDLL_Initialize参数类型
    printf("\n测试VMMDLL_Initialize参数类型:\n");
    
    LPCSTR argv[] = {
        "",
        "-device", 
        "vmware://ro=1,id=1234",
        "-v"
    };
    
    VMM_HANDLE handle = VMMDLL_Initialize(4, argv);
    if (handle) {
        printf("✓ VMMDLL_Initialize调用成功\n");
    } else {
        printf("✗ VMMDLL_Initialize调用失败\n");
        return 1;
    }
    
    // 测试数据包头初始化
    printf("\n测试数据包头初始化:\n");
    packet_header_t header;
    init_packet_header(&header, CMD_INIT, 4, 1);
    
    printf("  Magic: 0x%08X (期望: 0x%08X)\n", header.magic, PACKET_MAGIC);
    printf("  Version: %u (期望: %u)\n", header.version, PROTOCOL_VERSION);
    printf("  Command: 0x%02X (期望: 0x%02X)\n", header.command, CMD_INIT);
    
    if (header.magic == PACKET_MAGIC && 
        header.version == PROTOCOL_VERSION && 
        header.command == CMD_INIT) {
        printf("✓ 数据包头初始化正确\n");
    } else {
        printf("✗ 数据包头初始化错误\n");
        return 1;
    }
    
    printf("\n所有测试通过！编译修复成功。\n");
    return 0;
}
