#!/usr/bin/env python3
"""
MemProcFS TCP Client - Python Simple Example

Simple Python client using standard socket library.
No external dependencies required - only uses Python standard library.

Features:
- Memory read/write operations
- Process enumeration and lookup
- Module base address resolution
- Cross-platform socket communication
- No external dependencies

Usage:
    python client_python_simple.py
"""

import struct
import sys
import time
import socket

# Protocol constants
PACKET_MAGIC = 0x4D505246  # "MPRF"
PROTOCOL_VERSION = 1
DEFAULT_SERVER_IP = "127.0.0.1"
DEFAULT_PORT = 12345

# Command types
CMD_PING = 0x01
CMD_GET_VERSION = 0x02
CMD_READ_MEMORY = 0x10
CMD_WRITE_MEMORY = 0x11
CMD_READ_PHYSICAL = 0x12
CMD_WRITE_PHYSICAL = 0x13
CMD_GET_PROCESS_LIST = 0x20
CMD_GET_PROCESS_BY_NAME = 0x21
CMD_GET_PROCESS_INFO = 0x22
CMD_GET_MODULE_BASE = 0x30
CMD_GET_MODULE_LIST = 0x31
CMD_GET_MODULE_INFO = 0x32
CMD_RESPONSE = 0x80
CMD_ERROR = 0xFF

# Status codes
STATUS_SUCCESS = 0x00
STATUS_ERROR_INVALID_CMD = 0x01
STATUS_ERROR_INVALID_PARAM = 0x02
STATUS_ERROR_READ_FAILED = 0x03
STATUS_ERROR_WRITE_FAILED = 0x04
STATUS_ERROR_PROCESS_NOT_FOUND = 0x05
STATUS_ERROR_MODULE_NOT_FOUND = 0x06

class MemProcFSClient:
    """MemProcFS TCP client using standard socket library."""

    def __init__(self, server_ip=DEFAULT_SERVER_IP, port=DEFAULT_PORT, vmware_process_id=None):
        self.server_ip = server_ip
        self.port = port
        self.vmware_process_id = vmware_process_id
        self.socket = None
        self.sequence = 1
        self.connected = False
    
    def connect(self):
        """Connect to the MemProcFS server."""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.server_ip, self.port))
            print(f"Connected to MemProcFS server at {self.server_ip}:{self.port}")

            # Send initialization command if VMware process ID is provided
            if self.vmware_process_id is not None:
                if not self._initialize_vmware_process():
                    return False

            self.connected = True
            return True
        except Exception as e:
            print(f"Connection failed: {e}")
            return False

    def _initialize_vmware_process(self):
        """Initialize with specific VMware process ID."""
        try:
            # Send init command
            header = struct.pack('<IBBHII', 0x4D505246, 1, 0x00, 0, 4, self.sequence)
            self.sequence += 1

            self._send_data(header)
            self._send_data(struct.pack('<I', self.vmware_process_id))

            # Receive response
            response_header = self._receive_data(16)
            response_data = self._receive_data(8)

            status = struct.unpack('<B', response_data[:1])[0]
            if status == 0:  # STATUS_SUCCESS
                print(f"Successfully initialized with VMware process ID {self.vmware_process_id}")
                return True
            else:
                print(f"Failed to initialize with VMware process ID {self.vmware_process_id}")
                return False
        except Exception as e:
            print(f"Initialization failed: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from the server."""
        if self.socket:
            self.socket.close()
            self.socket = None
            self.connected = False
            print("Disconnected from server")

    def _send_data(self, data):
        """Send data to the server."""
        if not isinstance(data, bytes):
            data = bytes(data)

        total_sent = 0
        while total_sent < len(data):
            sent = self.socket.send(data[total_sent:])
            if sent == 0:
                raise RuntimeError("Socket connection broken")
            total_sent += sent

    def _receive_data(self, size):
        """Receive data from the server."""
        data = b''
        while len(data) < size:
            chunk = self.socket.recv(size - len(data))
            if not chunk:
                raise RuntimeError("Socket connection broken")
            data += chunk
        return data
    
    def _send_request(self, command, data=b''):
        """Send a request to the server and receive response."""
        if not self.connected:
            raise RuntimeError("Not connected to server")
        
        if not isinstance(data, bytes):
            data = bytes(data)
        
        # Create packet header
        header = struct.pack('<IBBHII', 
                           PACKET_MAGIC,      # magic
                           PROTOCOL_VERSION,  # version
                           command,           # command
                           0,                 # flags
                           len(data),         # data_size
                           self.sequence)     # sequence
        
        # Send request
        self._send_data(header + data)
        self.sequence += 1
        
        # Receive response header
        response_header = self._receive_data(16)
        magic, version, cmd, flags, data_size, sequence = struct.unpack('<IBBHII', response_header)
        
        # Validate response header
        if magic != PACKET_MAGIC or version != PROTOCOL_VERSION or cmd != CMD_RESPONSE:
            raise RuntimeError("Invalid response header")
        
        # Receive response data
        response_data = b''
        if data_size > 0:
            response_data = self._receive_data(data_size)
        
        return response_data
    
    def ping(self):
        """Test connection to server."""
        try:
            response = self._send_request(CMD_PING)
            if len(response) >= 8:  # response_header_t size
                status = struct.unpack('<B', response[:1])[0]
                return status == STATUS_SUCCESS
            return False
        except Exception as e:
            print(f"Ping failed: {e}")
            return False
    
    def get_version(self):
        """Get protocol version from server."""
        try:
            response = self._send_request(CMD_GET_VERSION)
            if len(response) >= 8:  # response_header_t + version
                status = struct.unpack('<B', response[:1])[0]
                if status == STATUS_SUCCESS:
                    version = struct.unpack('<I', response[8:12])[0]
                    return version
            return None
        except Exception as e:
            print(f"Get version failed: {e}")
            return None
    
    def read_memory(self, process_id, address, size):
        """Read memory from a process."""
        try:
            # Create memory request
            request = struct.pack('<IQI', process_id, address, size)
            response = self._send_request(CMD_READ_MEMORY, request)
            
            if len(response) >= 8:  # response_header_t size
                status = struct.unpack('<B', response[:1])[0]
                if status == STATUS_SUCCESS and len(response) >= 12:  # + memory_response_t
                    bytes_read = struct.unpack('<I', response[8:12])[0]
                    memory_data = response[12:12+bytes_read]
                    return memory_data
            return None
        except Exception as e:
            print(f"Read memory failed: {e}")
            return None
    
    def get_process_by_name(self, process_name):
        """Get process ID by name."""
        try:
            # Pad process name to 260 bytes
            name_bytes = process_name.encode('utf-8')[:259] + b'\x00'
            name_bytes = name_bytes.ljust(260, b'\x00')
            
            response = self._send_request(CMD_GET_PROCESS_BY_NAME, name_bytes)
            
            if len(response) >= 8:  # response_header_t size
                status = struct.unpack('<B', response[:1])[0]
                if status == STATUS_SUCCESS and len(response) >= 12:  # + process_id
                    process_id = struct.unpack('<I', response[8:12])[0]
                    return process_id
            return None
        except Exception as e:
            print(f"Get process by name failed: {e}")
            return None
    
    def get_module_base(self, process_id, module_name):
        """Get module base address."""
        try:
            # Create module request
            name_bytes = module_name.encode('utf-8')[:259] + b'\x00'
            name_bytes = name_bytes.ljust(260, b'\x00')
            request = struct.pack('<I', process_id) + name_bytes
            
            response = self._send_request(CMD_GET_MODULE_BASE, request)
            
            if len(response) >= 8:  # response_header_t size
                status = struct.unpack('<B', response[:1])[0]
                if status == STATUS_SUCCESS and len(response) >= 16:  # + base_address
                    base_address = struct.unpack('<Q', response[8:16])[0]
                    return base_address
            return None
        except Exception as e:
            print(f"Get module base failed: {e}")
            return None

    # Simplified high-level API
    def read_memory_simple(self, process_id, address, size):
        """Read memory and return bytes directly."""
        try:
            return self.read_memory(process_id, address, size)
        except:
            return None

    def write_memory_simple(self, process_id, address, data):
        """Write memory data directly."""
        try:
            return self.write_memory(process_id, address, data)
        except:
            return 0

    def get_process_id(self, process_name):
        """Get process ID by name."""
        try:
            return self.get_process_by_name(process_name)
        except:
            return 0

    def get_module_base_simple(self, process_id, module_name):
        """Get module base address."""
        try:
            return self.get_module_base(process_id, module_name)
        except:
            return 0

def print_hex_dump(data, base_address=0):
    """Print data as a hex dump."""
    for i in range(0, len(data), 16):
        # Address
        addr = base_address + i
        line = f"{addr:08X}: "
        
        # Hex bytes
        hex_part = ""
        ascii_part = ""
        for j in range(16):
            if i + j < len(data):
                byte = data[i + j]
                hex_part += f"{byte:02X} "
                ascii_part += chr(byte) if 32 <= byte <= 126 else "."
            else:
                hex_part += "   "
                ascii_part += " "
        
        print(f"{line}{hex_part} |{ascii_part}|")

def main():
    """Main example function."""
    print("MemProcFS TCP Client - Python Simple Example")
    print("============================================\n")

    # Create client with VMware process ID
    vmware_pid = 9856  # Example VMware process ID
    print(f"Connecting with VMware process ID: {vmware_pid}")

    client = MemProcFSClient(vmware_process_id=vmware_pid)

    if not client.connect():
        print("Failed to connect to server")
        print("\nTroubleshooting:")
        print("1. Make sure the MemProcFS server is running")
        print("2. Check if VMware is running with a VM")
        print("3. Verify VMware process ID is correct")
        print("4. Verify server IP and port")
        return 1
    
    try:
        # Test ping
        print("Testing connection...")
        if client.ping():
            print("✓ Ping successful!\n")
        else:
            print("✗ Ping failed!")
            return 1
        
        # Get version
        version = client.get_version()
        if version is not None:
            print(f"Server protocol version: {version}\n")
        
        # Find notepad process
        print("Looking for notepad.exe...")
        notepad_pid = client.get_process_by_name("notepad.exe")
        
        if notepad_pid:
            print(f"✓ Found notepad.exe with PID: {notepad_pid}\n")
            
            # Get ntdll.dll base address
            print("Getting ntdll.dll base address...")
            ntdll_base = client.get_module_base(notepad_pid, "ntdll.dll")
            
            if ntdll_base:
                print(f"✓ ntdll.dll base address: 0x{ntdll_base:X}\n")
                
                # Read DOS header
                print("Reading DOS header...")
                dos_header = client.read_memory(notepad_pid, ntdll_base, 64)
                
                if dos_header:
                    print(f"✓ Read {len(dos_header)} bytes from DOS header")
                    print("DOS header hex dump:")
                    print_hex_dump(dos_header, ntdll_base)
                    
                    # Check DOS signature
                    if len(dos_header) >= 2:
                        sig = dos_header[0] + (dos_header[1] << 8)
                        if sig == 0x5A4D:  # "MZ"
                            print("\n✓ Valid DOS signature found!")
                        else:
                            print(f"\n⚠ Invalid DOS signature: 0x{sig:04X}")
                else:
                    print("✗ Failed to read DOS header")
            else:
                print("✗ Failed to get ntdll.dll base address")
        else:
            print("✗ notepad.exe not found")
            print("Make sure notepad.exe is running in the target VM")

    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        client.disconnect()
    
    print("\nPython TCP client example completed.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
