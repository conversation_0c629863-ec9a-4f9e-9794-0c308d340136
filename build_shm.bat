@echo off
setlocal enabledelayedexpansion

echo MemProcFS Shared Memory Server v2.0 - Build
echo ===========================================

:: 检查参数
if "%~1"=="" (
    echo Usage: build_shm.bat "C:\path\to\MemProcFS"
    echo.
    echo Example: build_shm.bat "C:\MemProcFS"
    echo.
    echo The MemProcFS directory should contain:
    echo   - vmmdll.h
    echo   - vmmdll.lib
    echo   - vmmdll.dll
    exit /b 1
)

set MEMPROCFS_PATH=%~1

:: 验证MemProcFS路径
if not exist "%MEMPROCFS_PATH%\vmmdll.h" (
    echo Error: vmmdll.h not found in %MEMPROCFS_PATH%
    exit /b 1
)

if not exist "%MEMPROCFS_PATH%\vmmdll.lib" (
    echo Error: vmmdll.lib not found in %MEMPROCFS_PATH%
    exit /b 1
)

if not exist "%MEMPROCFS_PATH%\vmmdll.dll" (
    echo Error: vmmdll.dll not found in %MEMPROCFS_PATH%
    exit /b 1
)

echo MemProcFS path: %MEMPROCFS_PATH%

:: 检查编译器
where cl >nul 2>&1
if errorlevel 1 (
    echo Error: Visual Studio compiler (cl.exe) not found
    echo Please run this script from a Visual Studio Developer Command Prompt
    exit /b 1
)

:: 创建输出目录
if not exist "bin" mkdir bin
if not exist "bin\Release" mkdir bin\Release

echo.
echo Building MemProcFS Shared Memory Server...

:: 编译共享内存服务器
cl /EHsc /O2 /MD ^
   /I"%MEMPROCFS_PATH%" ^
   memprocfs_server_shm.cpp ^
   /link "%MEMPROCFS_PATH%\vmmdll.lib" kernel32.lib ^
   /OUT:bin\Release\memprocfs_server_shm.exe

if errorlevel 1 (
    echo.
    echo Server build failed!
    echo.
    echo Common issues:
    echo 1. Make sure you're running from Visual Studio Developer Command Prompt
    echo 2. Check that MemProcFS path is correct: %MEMPROCFS_PATH%
    echo 3. Verify vmmdll.lib exists in the MemProcFS directory
    exit /b 1
)

:: 编译C客户端
echo.
echo Building C client...
cl /EHsc /O2 /MD ^
   client_shm.c client_shm_impl.c ^
   /link kernel32.lib ^
   /OUT:bin\Release\client_shm.exe

if errorlevel 1 (
    echo.
    echo C client build failed!
    exit /b 1
)

:: 编译PCILeech执行器
echo.
echo Building PCILeech executor...
cl /EHsc /O2 /MD ^
   pcileech_executor.c client_shm_impl.c ^
   /link kernel32.lib ^
   /OUT:bin\Release\pcileech_executor.exe

if errorlevel 1 (
    echo.
    echo PCILeech executor build failed!
    exit /b 1
)

:: 编译内核shellcode生成器
echo.
echo Building kernel shellcode generator...
cl /EHsc /O2 /MD ^
   kernel_shellcode_generator.c ^
   /link kernel32.lib ^
   /OUT:bin\Release\kernel_shellcode_generator.exe

if errorlevel 1 (
    echo.
    echo Kernel shellcode generator build failed!
    exit /b 1
)

:: 复制必要文件
echo.
echo Copying files...

copy "%MEMPROCFS_PATH%\vmmdll.dll" "bin\Release\" >nul
copy "shared_memory_protocol.h" "bin\Release\" >nul
copy "pcileech_shellcode_examples.h" "bin\Release\" >nul
copy "client_shm.py" "bin\Release\" >nul

:: 清理临时文件
del *.obj >nul 2>&1

echo.
echo Build completed successfully!
echo.
echo Executables are in: bin\Release\
echo   - memprocfs_server_shm.exe (Shared Memory Server)
echo   - client_shm.exe (C Client)
echo   - pcileech_executor.exe (PCILeech-style Code Executor)
echo   - kernel_shellcode_generator.exe (Kernel Shellcode Generator)
echo   - client_shm.py (Python Client)
echo   - shared_memory_protocol.h (Protocol header)
echo   - pcileech_shellcode_examples.h (PCILeech Shellcode Examples)
echo.
echo To run the server:
echo   cd bin\Release
echo   memprocfs_server_shm.exe
echo.
echo To test the C client:
echo   client_shm.exe
echo.
echo To test PCILeech-style code execution:
echo   pcileech_executor.exe
echo.
echo To generate kernel shellcode:
echo   kernel_shellcode_generator.exe
echo.
echo To test the Python client:
echo   python client_shm.py
echo.
echo Features:
echo   - Shared memory communication (high performance)
echo   - Simplified protocol and client APIs
echo   - Server handles all complex logic
echo   - Support for multiple VMware processes
echo   - Windows native implementation
echo.
