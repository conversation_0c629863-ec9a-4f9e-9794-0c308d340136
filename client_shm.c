/*
 * MemProcFS Shared Memory Client
 *
 * 简化的共享内存客户端，支持32位和64位
 * 所有复杂逻辑都在服务端处理
 */

#define _CRT_SECURE_NO_WARNINGS
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>

#ifdef _WIN32
    #include <windows.h>
#else
    #error "This client only supports Windows"
#endif

#include "shared_memory_protocol.h"

// 客户端结构和函数声明现在在shared_memory_protocol.h中
// 函数实现在client_shm_impl.c中

// 示例程序
int main() {
    printf("MemProcFS Shared Memory Client Example\n");
    printf("======================================\n\n");

    memprocfs_client_t client;

    // 初始化客户端 (使用示例VMware进程ID)
    uint32_t vmware_pid = 12280;
    printf("Initializing with VMware process ID: %u\n", vmware_pid);

    if (!memprocfs_init(&client, vmware_pid)) {
        printf("Failed to initialize client\n");
        return 1;
    }

    // 获取进程ID
    printf("\nLooking for notepad.exe...\n");
    uint32_t pid = memprocfs_get_process_id(&client, "notepad.exe");
    if (pid > 0) {
        printf("Found notepad.exe with PID: %u\n", pid);

        // 获取模块基址
        printf("Getting ntdll.dll base address...\n");
        uint64_t base = memprocfs_get_module_base(&client, pid, "ntdll.dll");
        if (base > 0) {
            printf("ntdll.dll base address: 0x%llX\n", base);

            // 读取内存
            printf("Reading 64 bytes from memory...\n");
            uint8_t buffer[64];
            int bytes_read = memprocfs_read_memory(&client, pid, base, buffer, sizeof(buffer));
            if (bytes_read > 0) {
                printf("Successfully read %d bytes\n", bytes_read);
                printf("First 16 bytes: ");
                for (int i = 0; i < 16 && i < bytes_read; i++) {
                    printf("%02X ", buffer[i]);
                }
                printf("\n");
            } else {
                printf("Failed to read memory\n");
            }
        } else {
            printf("ntdll.dll not found\n");
        }
    } else {
        printf("notepad.exe not found (make sure it's running in the VM)\n");
    }

    // 清理
    memprocfs_cleanup(&client);

    printf("\nClient example completed.\n");
    return 0;
}
