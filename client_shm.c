/*
 * MemProcFS Shared Memory Client
 * 
 * 简化的共享内存客户端，支持32位和64位
 * 所有复杂逻辑都在服务端处理
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>

#ifdef _WIN32
    #include <windows.h>
#else
    #error "This client only supports Windows"
#endif

#include "shared_memory_protocol.h"

// 客户端结构
typedef struct {
    HANDLE hSharedMemory;
    HANDLE hMutex;
    HANDLE hRequestEvent;
    HANDLE hResponseEvent;
    shared_memory_t* pSharedMemory;
    uint32_t sequence;
} memprocfs_client_t;

// 初始化客户端
int memprocfs_init(memprocfs_client_t* client, uint32_t vmware_process_id) {
    if (!client) return 0;

    memset(client, 0, sizeof(memprocfs_client_t));
    client->sequence = 1;
    
    // 打开共享内存
    client->hSharedMemory = OpenFileMappingA(FILE_MAP_ALL_ACCESS, FALSE, MEMPROCFS_SHM_NAME);
    if (!client->hSharedMemory) {
        printf("Failed to open shared memory. Is server running?\n");
        return 0;
    }
    
    // 映射共享内存
    client->pSharedMemory = (shared_memory_t*)MapViewOfFile(
        client->hSharedMemory, FILE_MAP_ALL_ACCESS, 0, 0, MEMPROCFS_SHM_SIZE);
    if (!client->pSharedMemory) {
        printf("Failed to map shared memory\n");
        CloseHandle(client->hSharedMemory);
        return 0;
    }
    
    // 打开互斥锁和事件
    client->hMutex = OpenMutexA(MUTEX_ALL_ACCESS, FALSE, MEMPROCFS_MUTEX_NAME);
    client->hRequestEvent = OpenEventA(EVENT_ALL_ACCESS, FALSE, MEMPROCFS_REQUEST_EVENT);
    client->hResponseEvent = OpenEventA(EVENT_ALL_ACCESS, FALSE, MEMPROCFS_RESPONSE_EVENT);
    
    if (!client->hMutex || !client->hRequestEvent || !client->hResponseEvent) {
        printf("Failed to open synchronization objects\n");
        memprocfs_cleanup(client);
        return 0;
    }
    
    // 发送初始化请求
    WaitForSingleObject(client->hMutex, INFINITE);
    
    init_request_header(&client->pSharedMemory->request_header, CMD_INIT, sizeof(init_request_t), client->sequence++);
    
    init_request_t* request = (init_request_t*)client->pSharedMemory->request_data;
    request->vmware_process_id = vmware_process_id;
    
    SetEvent(client->hRequestEvent);
    ReleaseMutex(client->hMutex);
    
    // 等待响应
    if (WaitForSingleObject(client->hResponseEvent, 5000) != WAIT_OBJECT_0) {
        printf("Timeout waiting for init response\n");
        memprocfs_cleanup(client);
        return 0;
    }
    
    if (client->pSharedMemory->response_header.status != STATUS_SUCCESS) {
        printf("Failed to initialize VMware process %u\n", vmware_process_id);
        memprocfs_cleanup(client);
        return 0;
    }
    
    printf("Successfully initialized with VMware process %u\n", vmware_process_id);
    return 1;
}

// 清理客户端
void memprocfs_cleanup(memprocfs_client_t* client) {
    if (!client) return;
    
    if (client->hResponseEvent) {
        CloseHandle(client->hResponseEvent);
        client->hResponseEvent = NULL;
    }
    
    if (client->hRequestEvent) {
        CloseHandle(client->hRequestEvent);
        client->hRequestEvent = NULL;
    }
    
    if (client->hMutex) {
        CloseHandle(client->hMutex);
        client->hMutex = NULL;
    }
    
    if (client->pSharedMemory) {
        UnmapViewOfFile(client->pSharedMemory);
        client->pSharedMemory = NULL;
    }
    
    if (client->hSharedMemory) {
        CloseHandle(client->hSharedMemory);
        client->hSharedMemory = NULL;
    }
}

// 获取进程ID
uint32_t memprocfs_get_process_id(memprocfs_client_t* client, const char* process_name) {
    if (!client || !client->pSharedMemory || !process_name) return 0;
    
    WaitForSingleObject(client->hMutex, INFINITE);
    
    init_request_header(&client->pSharedMemory->request_header, CMD_GET_PROCESS_ID, sizeof(get_process_id_request_t), client->sequence++);
    
    get_process_id_request_t* request = (get_process_id_request_t*)client->pSharedMemory->request_data;
    strncpy(request->process_name, process_name, sizeof(request->process_name) - 1);
    request->process_name[sizeof(request->process_name) - 1] = '\0';
    
    SetEvent(client->hRequestEvent);
    ReleaseMutex(client->hMutex);
    
    // 等待响应
    if (WaitForSingleObject(client->hResponseEvent, 5000) != WAIT_OBJECT_0) {
        printf("Timeout waiting for get_process_id response\n");
        return 0;
    }
    
    if (client->pSharedMemory->response_header.status != STATUS_SUCCESS) {
        return 0;
    }
    
    get_process_id_response_t* response = (get_process_id_response_t*)client->pSharedMemory->response_data;
    return response->process_id;
}

// 获取模块基址
uint64_t memprocfs_get_module_base(memprocfs_client_t* client, uint32_t process_id, const char* module_name) {
    if (!client || !client->pSharedMemory || !module_name) return 0;
    
    WaitForSingleObject(client->hMutex, INFINITE);
    
    init_request_header(&client->pSharedMemory->request_header, CMD_GET_MODULE_BASE, sizeof(get_module_base_request_t), client->sequence++);
    
    get_module_base_request_t* request = (get_module_base_request_t*)client->pSharedMemory->request_data;
    request->process_id = process_id;
    strncpy(request->module_name, module_name, sizeof(request->module_name) - 1);
    request->module_name[sizeof(request->module_name) - 1] = '\0';
    
    SetEvent(client->hRequestEvent);
    ReleaseMutex(client->hMutex);
    
    // 等待响应
    if (WaitForSingleObject(client->hResponseEvent, 5000) != WAIT_OBJECT_0) {
        printf("Timeout waiting for get_module_base response\n");
        return 0;
    }
    
    if (client->pSharedMemory->response_header.status != STATUS_SUCCESS) {
        return 0;
    }
    
    get_module_base_response_t* response = (get_module_base_response_t*)client->pSharedMemory->response_data;
    return response->base_address;
}

// 读取内存
int memprocfs_read_memory(memprocfs_client_t* client, uint32_t process_id, uint64_t address, void* buffer, uint32_t size) {
    if (!client || !client->pSharedMemory || !buffer || size == 0) return 0;
    
    WaitForSingleObject(client->hMutex, INFINITE);
    
    init_request_header(&client->pSharedMemory->request_header, CMD_READ_MEMORY, sizeof(memory_request_t), client->sequence++);
    
    memory_request_t* request = (memory_request_t*)client->pSharedMemory->request_data;
    request->process_id = process_id;
    request->address = address;
    request->size = size;
    
    SetEvent(client->hRequestEvent);
    ReleaseMutex(client->hMutex);
    
    // 等待响应
    if (WaitForSingleObject(client->hResponseEvent, 5000) != WAIT_OBJECT_0) {
        printf("Timeout waiting for read_memory response\n");
        return 0;
    }
    
    if (client->pSharedMemory->response_header.status != STATUS_SUCCESS) {
        return 0;
    }
    
    memory_response_t* response = (memory_response_t*)client->pSharedMemory->response_data;
    uint8_t* data = client->pSharedMemory->response_data + sizeof(memory_response_t);
    
    memcpy(buffer, data, response->bytes_processed);
    return response->bytes_processed;
}

// 写入内存
int memprocfs_write_memory(memprocfs_client_t* client, uint32_t process_id, uint64_t address, const void* buffer, uint32_t size) {
    if (!client || !client->pSharedMemory || !buffer || size == 0) return 0;
    
    WaitForSingleObject(client->hMutex, INFINITE);
    
    init_request_header(&client->pSharedMemory->request_header, CMD_WRITE_MEMORY, sizeof(memory_request_t) + size, client->sequence++);
    
    memory_request_t* request = (memory_request_t*)client->pSharedMemory->request_data;
    request->process_id = process_id;
    request->address = address;
    request->size = size;
    
    // 复制数据
    memcpy(client->pSharedMemory->request_data + sizeof(memory_request_t), buffer, size);
    
    SetEvent(client->hRequestEvent);
    ReleaseMutex(client->hMutex);
    
    // 等待响应
    if (WaitForSingleObject(client->hResponseEvent, 5000) != WAIT_OBJECT_0) {
        printf("Timeout waiting for write_memory response\n");
        return 0;
    }
    
    if (client->pSharedMemory->response_header.status != STATUS_SUCCESS) {
        return 0;
    }
    
    memory_response_t* response = (memory_response_t*)client->pSharedMemory->response_data;
    return response->bytes_processed;
}

// 示例程序
int main() {
    printf("MemProcFS Shared Memory Client Example\n");
    printf("======================================\n\n");
    
    memprocfs_client_t client;
    
    // 初始化客户端 (使用示例VMware进程ID)
    uint32_t vmware_pid = 12345;
    printf("Initializing with VMware process ID: %u\n", vmware_pid);
    
    if (!memprocfs_init(&client, vmware_pid)) {
        printf("Failed to initialize client\n");
        return 1;
    }
    
    // 获取进程ID
    printf("\nLooking for notepad.exe...\n");
    uint32_t pid = memprocfs_get_process_id(&client, "notepad.exe");
    if (pid > 0) {
        printf("Found notepad.exe with PID: %u\n", pid);
        
        // 获取模块基址
        printf("Getting ntdll.dll base address...\n");
        uint64_t base = memprocfs_get_module_base(&client, pid, "ntdll.dll");
        if (base > 0) {
            printf("ntdll.dll base address: 0x%llX\n", base);
            
            // 读取内存
            printf("Reading 64 bytes from memory...\n");
            uint8_t buffer[64];
            int bytes_read = memprocfs_read_memory(&client, pid, base, buffer, sizeof(buffer));
            if (bytes_read > 0) {
                printf("Successfully read %d bytes\n", bytes_read);
                printf("First 16 bytes: ");
                for (int i = 0; i < 16 && i < bytes_read; i++) {
                    printf("%02X ", buffer[i]);
                }
                printf("\n");
            } else {
                printf("Failed to read memory\n");
            }
        } else {
            printf("ntdll.dll not found\n");
        }
    } else {
        printf("notepad.exe not found (make sure it's running in the VM)\n");
    }
    
    // 清理
    memprocfs_cleanup(&client);
    
    printf("\nClient example completed.\n");
    return 0;
}
