/*
 * MemProcFS TCP Client - C Example
 *
 * Simple C client demonstrating how to communicate with the MemProcFS TCP server.
 * This client can be compiled as 32-bit or 64-bit and works with any C compiler.
 *
 * Features:
 * - Memory read/write operations
 * - Process enumeration and lookup
 * - Module base address resolution
 * - Simple error handling
 *
 * Compile: gcc -o client_c client_c.c -lws2_32 (Windows)
 *          gcc -o client_c client_c.c (Linux - with adaptation)
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    typedef int socklen_t;
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define closesocket close
#endif

#include "protocol.h"

typedef struct {
    SOCKET socket;
    uint32_t sequence;
} memprocfs_client_t;

// Initialize client with VMware process ID
int memprocfs_client_init(memprocfs_client_t* client, const char* server_ip, uint16_t port, uint32_t vmware_process_id) {
#ifdef _WIN32
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        printf("WSAStartup failed\n");
        return 0;
    }
#endif

    client->socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (client->socket == INVALID_SOCKET) {
        printf("Socket creation failed\n");
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }

    struct sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(port);

#ifdef _WIN32
    serverAddr.sin_addr.s_addr = inet_addr(server_ip);
#else
    inet_pton(AF_INET, server_ip, &serverAddr.sin_addr);
#endif

    if (connect(client->socket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        printf("Connection failed\n");
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }

    client->sequence = 1;
    printf("Connected to MemProcFS server at %s:%d\n", server_ip, port);

    // Send initialization command with VMware process ID
    packet_header_t header;
    init_packet_header(&header, CMD_INIT, sizeof(uint32_t), client->sequence++);

    if (!send_data(client->socket, &header, sizeof(header))) {
        printf("Failed to send init header\n");
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }

    if (!send_data(client->socket, &vmware_process_id, sizeof(vmware_process_id))) {
        printf("Failed to send VMware process ID\n");
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }

    // Receive response
    packet_header_t response_header;
    if (!receive_data(client->socket, &response_header, sizeof(response_header))) {
        printf("Failed to receive init response header\n");
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }

    response_header_t response_data;
    if (!receive_data(client->socket, &response_data, sizeof(response_data))) {
        printf("Failed to receive init response data\n");
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }

    if (response_data.status != STATUS_SUCCESS) {
        printf("Server initialization failed with VMware process ID %u\n", vmware_process_id);
        closesocket(client->socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 0;
    }

    printf("Successfully initialized with VMware process ID %u\n", vmware_process_id);
    return 1;
}

// Cleanup client
void memprocfs_client_cleanup(memprocfs_client_t* client) {
    if (client->socket != INVALID_SOCKET) {
        closesocket(client->socket);
        client->socket = INVALID_SOCKET;
    }
#ifdef _WIN32
    WSACleanup();
#endif
}

// Send data
int send_data(SOCKET socket, const void* buffer, int size) {
    int totalSent = 0;
    const char* ptr = (const char*)buffer;

    while (totalSent < size) {
        int sent = send(socket, ptr + totalSent, size - totalSent, 0);
        if (sent == SOCKET_ERROR) {
            return 0;
        }
        totalSent += sent;
    }
    return 1;
}

// Receive data
int receive_data(SOCKET socket, void* buffer, int size) {
    int totalReceived = 0;
    char* ptr = (char*)buffer;

    while (totalReceived < size) {
        int received = recv(socket, ptr + totalReceived, size - totalReceived, 0);
        if (received <= 0) {
            return 0;
        }
        totalReceived += received;
    }
    return 1;
}

// Send request and receive response
int send_request(memprocfs_client_t* client, uint8_t command, const void* data, uint32_t dataSize,
                 void* responseBuffer, uint32_t* responseSize) {
    // Send request header
    packet_header_t requestHeader;
    init_packet_header(&requestHeader, command, dataSize, client->sequence++);

    if (!send_data(client->socket, &requestHeader, sizeof(requestHeader))) {
        printf("Failed to send request header\n");
        return 0;
    }

    // Send request data if any
    if (data && dataSize > 0) {
        if (!send_data(client->socket, data, dataSize)) {
            printf("Failed to send request data\n");
            return 0;
        }
    }

    // Receive response header
    packet_header_t responseHeader;
    if (!receive_data(client->socket, &responseHeader, sizeof(responseHeader))) {
        printf("Failed to receive response header\n");
        return 0;
    }

    // Validate response header
    if (!validate_packet_header(&responseHeader) || responseHeader.command != CMD_RESPONSE) {
        printf("Invalid response header\n");
        return 0;
    }

    // Receive response data
    if (responseHeader.data_size > 0) {
        if (responseBuffer && *responseSize >= responseHeader.data_size) {
            if (!receive_data(client->socket, responseBuffer, responseHeader.data_size)) {
                printf("Failed to receive response data\n");
                return 0;
            }
            *responseSize = responseHeader.data_size;
        } else {
            printf("Response buffer too small\n");
            return 0;
        }
    } else {
        *responseSize = 0;
    }

    return 1;
}

// Ping server
int memprocfs_ping(memprocfs_client_t* client) {
    uint8_t responseBuffer[64];
    uint32_t responseSize = sizeof(responseBuffer);
    
    if (!send_request(client, CMD_PING, NULL, 0, responseBuffer, &responseSize)) {
        return 0;
    }
    
    if (responseSize >= sizeof(response_header_t)) {
        response_header_t* response = (response_header_t*)responseBuffer;
        return response->status == STATUS_SUCCESS;
    }
    
    return 0;
}

// Read memory
int memprocfs_read_memory(memprocfs_client_t* client, uint32_t processId, uint64_t address, 
                          void* buffer, uint32_t size, uint32_t* bytesRead) {
    memory_request_t request;
    request.process_id = processId;
    request.address = address;
    request.size = size;
    
    uint8_t responseBuffer[sizeof(response_header_t) + sizeof(memory_response_t) + 4096];
    uint32_t responseSize = sizeof(responseBuffer);
    
    if (!send_request(client, CMD_READ_MEMORY, &request, sizeof(request), responseBuffer, &responseSize)) {
        return 0;
    }
    
    if (responseSize >= sizeof(response_header_t)) {
        response_header_t* responseHeader = (response_header_t*)responseBuffer;
        if (responseHeader->status == STATUS_SUCCESS && responseSize >= sizeof(response_header_t) + sizeof(memory_response_t)) {
            memory_response_t* memoryResponse = (memory_response_t*)(responseBuffer + sizeof(response_header_t));
            uint8_t* memoryData = responseBuffer + sizeof(response_header_t) + sizeof(memory_response_t);
            
            uint32_t copySize = memoryResponse->bytes_processed;
            if (copySize > size) copySize = size;
            
            memcpy(buffer, memoryData, copySize);
            if (bytesRead) *bytesRead = copySize;
            return 1;
        }
    }
    
    return 0;
}

// Get process by name
int memprocfs_get_process_by_name(memprocfs_client_t* client, const char* processName, uint32_t* processId) {
    process_by_name_request_t request;
    strncpy(request.process_name, processName, sizeof(request.process_name) - 1);
    request.process_name[sizeof(request.process_name) - 1] = '\0';
    
    uint8_t responseBuffer[sizeof(response_header_t) + sizeof(process_by_name_response_t)];
    uint32_t responseSize = sizeof(responseBuffer);
    
    if (!send_request(client, CMD_GET_PROCESS_BY_NAME, &request, sizeof(request), responseBuffer, &responseSize)) {
        return 0;
    }
    
    if (responseSize >= sizeof(response_header_t)) {
        response_header_t* responseHeader = (response_header_t*)responseBuffer;
        if (responseHeader->status == STATUS_SUCCESS && responseSize >= sizeof(response_header_t) + sizeof(process_by_name_response_t)) {
            process_by_name_response_t* response = (process_by_name_response_t*)(responseBuffer + sizeof(response_header_t));
            *processId = response->process_id;
            return 1;
        }
    }
    
    return 0;
}



// High-level API functions
int memprocfs_read_memory_simple(memprocfs_client_t* client, uint32_t process_id, uint64_t address, void* buffer, uint32_t size) {
    memory_request_t request;
    request.process_id = process_id;
    request.address = address;
    request.size = size;

    uint32_t response_size = sizeof(memory_response_t) + size;
    uint8_t* response_buffer = malloc(response_size);
    if (!response_buffer) return 0;

    int result = send_request(client, CMD_READ_MEMORY, &request, sizeof(request), response_buffer, &response_size);
    if (result && response_size >= sizeof(memory_response_t)) {
        memory_response_t* response = (memory_response_t*)response_buffer;
        if (response->bytes_processed > 0) {
            memcpy(buffer, response_buffer + sizeof(memory_response_t), response->bytes_processed);
            free(response_buffer);
            return response->bytes_processed;
        }
    }

    free(response_buffer);
    return 0;
}

int memprocfs_write_memory_simple(memprocfs_client_t* client, uint32_t process_id, uint64_t address, const void* buffer, uint32_t size) {
    uint32_t request_size = sizeof(memory_request_t) + size;
    uint8_t* request_buffer = malloc(request_size);
    if (!request_buffer) return 0;

    memory_request_t* request = (memory_request_t*)request_buffer;
    request->process_id = process_id;
    request->address = address;
    request->size = size;
    memcpy(request_buffer + sizeof(memory_request_t), buffer, size);

    // 分配足够大的缓冲区来接收完整响应
    uint8_t responseBuffer[sizeof(response_header_t) + sizeof(memory_response_t)];
    uint32_t response_size = sizeof(responseBuffer);

    int result = send_request(client, CMD_WRITE_MEMORY, request_buffer, request_size, responseBuffer, &response_size);
    free(request_buffer);

    if (result && response_size >= sizeof(response_header_t)) {
        response_header_t* responseHeader = (response_header_t*)responseBuffer;
        if (responseHeader->status == STATUS_SUCCESS && response_size >= sizeof(response_header_t) + sizeof(memory_response_t)) {
            memory_response_t* response = (memory_response_t*)(responseBuffer + sizeof(response_header_t));
            return response->bytes_processed;
        }
    }

    return 0;
}

uint32_t memprocfs_get_process_id(memprocfs_client_t* client, const char* process_name) {
    process_by_name_request_t request;
    strncpy(request.process_name, process_name, sizeof(request.process_name) - 1);
    request.process_name[sizeof(request.process_name) - 1] = '\0';

    // 分配足够大的缓冲区来接收完整响应
    uint8_t responseBuffer[sizeof(response_header_t) + sizeof(process_by_name_response_t)];
    uint32_t response_size = sizeof(responseBuffer);

    if (send_request(client, CMD_GET_PROCESS_BY_NAME, &request, sizeof(request), responseBuffer, &response_size)) {
        if (response_size >= sizeof(response_header_t)) {
            response_header_t* responseHeader = (response_header_t*)responseBuffer;
            if (responseHeader->status == STATUS_SUCCESS && response_size >= sizeof(response_header_t) + sizeof(process_by_name_response_t)) {
                process_by_name_response_t* response = (process_by_name_response_t*)(responseBuffer + sizeof(response_header_t));
                return response->process_id;
            }
        }
    }
    return 0;
}

uint64_t memprocfs_get_module_base(memprocfs_client_t* client, uint32_t process_id, const char* module_name) {
    module_request_t request;
    request.process_id = process_id;
    strncpy(request.module_name, module_name, sizeof(request.module_name) - 1);
    request.module_name[sizeof(request.module_name) - 1] = '\0';

    // 分配足够大的缓冲区来接收完整响应
    uint8_t responseBuffer[sizeof(response_header_t) + sizeof(module_base_response_t)];
    uint32_t response_size = sizeof(responseBuffer);

    if (send_request(client, CMD_GET_MODULE_BASE, &request, sizeof(request), responseBuffer, &response_size)) {
        if (response_size >= sizeof(response_header_t)) {
            response_header_t* responseHeader = (response_header_t*)responseBuffer;
            if (responseHeader->status == STATUS_SUCCESS && response_size >= sizeof(response_header_t) + sizeof(module_base_response_t)) {
                module_base_response_t* response = (module_base_response_t*)(responseBuffer + sizeof(response_header_t));
                return response->base_address;
            }
        }
    }
    return 0;
}

// Example usage
int main() {
    printf("MemProcFS TCP Client - C Example\n");
    printf("================================\n\n");

    memprocfs_client_t client;

    // Connect to server with VMware process ID
    uint32_t vmware_pid = 9856;  // Example VMware process ID
    printf("Connecting with VMware process ID: %u\n", vmware_pid);

    if (!memprocfs_client_init(&client, "127.0.0.1", MEMPROCFS_DEFAULT_PORT, vmware_pid)) {
        printf("Failed to connect to server\n");
        return 1;
    }
    
    // Test simple API
    printf("Testing simplified API...\n");

    // Get process by name
    printf("\nLooking for notepad.exe...\n");
    uint32_t pid = memprocfs_get_process_id(&client, "notepad.exe");
    if (pid > 0) {
        printf("✓ Found notepad.exe with PID: %u\n", pid);

        // Get module base
        uint64_t ntdll_base = memprocfs_get_module_base(&client, pid, "ntdll.dll");
        if (ntdll_base > 0) {
            printf("✓ ntdll.dll base address: 0x%llX\n", ntdll_base);

            // Read memory
            uint8_t buffer[64];
            int bytes_read = memprocfs_read_memory_simple(&client, pid, ntdll_base, buffer, sizeof(buffer));
            if (bytes_read > 0) {
                printf("✓ Read %d bytes from memory\n", bytes_read);
                printf("  First 16 bytes: ");
                for (int i = 0; i < 16 && i < bytes_read; i++) {
                    printf("%02X ", buffer[i]);
                }
                printf("\n");

                // Test write (write back the same data)
                int bytes_written = memprocfs_write_memory_simple(&client, pid, ntdll_base, buffer, 16);
                if (bytes_written > 0) {
                    printf("✓ Wrote %d bytes to memory\n", bytes_written);
                } else {
                    printf("✗ Failed to write memory\n");
                }
            } else {
                printf("✗ Failed to read memory\n");
            }
        } else {
            printf("✗ Failed to get ntdll.dll base address\n");
        }
    } else {
        printf("✗ notepad.exe not found (make sure it's running)\n");
    }
    
    // Cleanup
    memprocfs_client_cleanup(&client);
    printf("\nClient example completed.\n");
    
    return 0;
}
