# MemProcFS TCP Server v2.0 - 多VMware进程支持

这是一个原生Windows TCP服务器，专为Windows系统设计，支持多个VMware进程的并发访问，允许任何编程语言的客户端程序通过TCP通信访问指定VMware虚拟机内存。

## 🎯 项目特点

- **多VMware进程**: 每个客户端可连接不同的VMware进程
- **跨语言支持**: 支持C、Python、易语言等任何能进行TCP通信的编程语言
- **原生Windows**: 使用Windows原生TCP Socket API，无需第三方库依赖
- **纯TCP通信**: 标准TCP Socket通信，无需特殊权限
- **简化客户端**: 服务端处理复杂逻辑，客户端API简单易用
- **实时日志**: 服务端显示所有客户端操作和结果
- **无锁设计**: 每个客户端独立VMM实例，无需线程锁
- **Windows专用**: 专为Windows系统优化，简化部署

## 🏗️ 架构设计

```
┌─────────────────┐ TCP+PID=9856 ┌─────────────────┐ VMware://id=9856 ┌─────────────┐
│  客户端A程序    │ ◄───────────► │                 │ ◄───────────────► │  VMware进程A │
│  (任何语言)     │               │                 │                   │   (PID=9856) │
└─────────────────┘               │                 │                   └─────────────┘
                                  │ 原生Windows TCP服务器 │
┌─────────────────┐ TCP+PID=1234 │  (原生Windows)   │ VMware://id=1234 ┌─────────────┐
│  客户端B程序    │ ◄───────────► │                 │ ◄───────────────► │  VMware进程B │
│  (任何语言)     │               │                 │                   │   (PID=1234) │
└─────────────────┘               └─────────────────┘                   └─────────────┘
```

## 📋 系统要求

### 服务器端 (64位)
- Windows 10/11 (64位)
- Visual Studio 2019+ 或 Build Tools
- MemProcFS v5.0+ (64位)
- VMware Workstation/Player
- 管理员权限

### 客户端 (任意架构)
- 支持TCP Socket的编程语言
- Windows/Linux/macOS (客户端跨平台)

## 🚀 快速开始

### 1. 准备环境

1. **安装POCO C++**:
   ```bash
   # 使用vcpkg (推荐)
   vcpkg install poco[net,util]:x64-windows

   # 或从官网下载编译
   # https://pocoproject.org/
   ```

2. **下载MemProcFS**:
   ```bash
   # 从GitHub下载最新版本
   https://github.com/ufrisk/MemProcFS/releases/latest
   # 解压到 C:\MemProcFS\ 或其他目录
   ```

3. **启动VMware**:
   - 运行VMware Workstation/Player
   - 启动至少一个虚拟机

4. **管理员权限**:
   - 以管理员身份运行所有程序

### 2. 编译服务器

#### 使用原生构建脚本 (推荐)
```bash
# 在Visual Studio Developer Command Prompt中运行
build_native.bat "C:\MemProcFS"

# 测试编译
test_native.bat
```

#### 使用CMake (原生版本)
```bash
mkdir build && cd build
cmake .. -DMEMPROCFS_ROOT="C:\MemProcFS" -f CMakeLists_native.txt
cmake --build . --config Release
```

### 3. 运行服务器

```bash
cd bin\Release
memprocfs_server.exe
```

服务器默认监听端口 `12345`，可以通过命令行参数修改：
```bash
memprocfs_server.exe 8080
```

服务器启动后会显示：
```
MemProcFS TCP Server v2.0
Multi-VMware Process Support
Listening on port: 12345
Each client will initialize with specific VMware process ID
Press Ctrl+C to stop the server
```

### 4. 运行客户端示例

#### C客户端
```bash
# Windows (使用MSVC)
cl client_c.c ws2_32.lib /Fe:client_c.exe

# Windows (使用MinGW)
gcc -o client_c client_c.c -lws2_32

# Linux
gcc -o client_c client_c.c

# 运行 (需要指定VMware进程ID)
client_c.exe
```

#### Python客户端

**推荐使用简化版** (无需额外依赖):
```bash
python client_python_simple.py
```

#### 易语言客户端

参考 `client_易语言.e` 文件，包含完整的示例代码和实现说明。

## 📖 API参考文档

详细的API参考请查看 [API_Reference.md](API_Reference.md)，包含：
- 完整的函数签名
- 参数说明
- 返回值说明
- 使用示例
- 错误处理

#### C#客户端
```bash
csc client_csharp.cs
client_csharp.exe
```

## 🚀 使用流程

### 1. 获取VMware进程ID
```bash
# 查看VMware进程
tasklist | findstr vmware
# 或使用进程管理器找到vmware-vmx.exe的PID
```

### 2. 客户端连接流程
1. **TCP连接**: 连接到服务器IP:端口
2. **发送初始化**: 发送CMD_INIT命令 + VMware进程ID
3. **接收确认**: 服务器返回初始化结果
4. **开始操作**: 使用简化API进行内存操作

### 3. 服务端日志示例
```
[*************:52341] Client connected
[*************:52341] INIT: Successfully initialized VMware process 9856
[*************:52341] get_process_by_name: notepad.exe
[*************:52341] get_process_by_name: SUCCESS - PID=1234
[*************:52341] get_module_base: PID=1234 Module=ntdll.dll
[*************:52341] get_module_base: SUCCESS - Base=0x77000000
[*************:52341] read_memory: PID=1234 Addr=0x77000000 Size=64
[*************:52341] read_memory: SUCCESS - 64 bytes
```

## 📡 通信协议

### 协议特点
- **初始化机制**: 每个客户端指定VMware进程ID
- **二进制格式**: 高效的数据传输
- **固定头部**: 16字节包头，易于解析
- **序列号**: 支持请求/响应匹配
- **错误处理**: 完善的状态码系统

### 包结构
```c
// 包头 (16字节)
struct PacketHeader {
    uint32_t magic;      // 魔数: 0x4D505246 ("MPRF")
    uint8_t  version;    // 协议版本: 1
    uint8_t  command;    // 命令类型
    uint16_t flags;      // 标志位 (保留)
    uint32_t data_size;  // 数据大小
    uint32_t sequence;   // 序列号
};
```

### 支持的命令

| 命令 | 值 | 描述 |
|------|----|----- |
| CMD_PING | 0x01 | 测试连接 |
| CMD_READ_MEMORY | 0x10 | 读取虚拟内存 |
| CMD_WRITE_MEMORY | 0x11 | 写入虚拟内存 |
| CMD_READ_PHYSICAL | 0x12 | 读取物理内存 |
| CMD_WRITE_PHYSICAL | 0x13 | 写入物理内存 |
| CMD_GET_PROCESS_BY_NAME | 0x21 | 根据名称查找进程 |
| CMD_GET_MODULE_BASE | 0x30 | 获取模块基址 |

## 💻 客户端示例

### Python示例 (简化API)
```python
from client_python_simple import MemProcFSClient

# 连接服务器并初始化VMware进程
client = MemProcFSClient(vmware_process_id=9856)
client.connect()

# 简化的API调用
pid = client.get_process_id("notepad.exe")
if pid:
    print(f"找到记事本进程: {pid}")

    # 获取模块基址
    ntdll_base = client.get_module_base_simple(pid, "ntdll.dll")
    if ntdll_base:
        print(f"ntdll.dll基址: 0x{ntdll_base:X}")

        # 读取内存
        data = client.read_memory_simple(pid, ntdll_base, 64)
        if data:
            print(f"读取了 {len(data)} 字节")
            print("前16字节:", " ".join(f"{b:02X}" for b in data[:16]))

client.disconnect()
```

### C示例 (简化API)
```c
#include "protocol.h"

int main() {
    memprocfs_client_t client;

    // 连接服务器并初始化VMware进程
    uint32_t vmware_pid = 9856;
    if (!memprocfs_client_init(&client, "127.0.0.1", 12345, vmware_pid)) {
        return 1;
    }

    // 简化的API调用
    uint32_t pid = memprocfs_get_process_id(&client, "notepad.exe");
    if (pid > 0) {
        printf("找到记事本进程: %u\n", pid);

        // 获取模块基址
        uint64_t base = memprocfs_get_module_base(&client, pid, "ntdll.dll");
        if (base > 0) {
            printf("ntdll.dll基址: 0x%llX\n", base);

            // 读取内存
            uint8_t buffer[64];
            int bytes_read = memprocfs_read_memory_simple(&client, pid, base, buffer, 64);
            if (bytes_read > 0) {
                printf("读取了 %d 字节\n", bytes_read);
            }
        }
    }

    memprocfs_client_cleanup(&client);
    return 0;
}
```

### 易语言示例
```
.版本 2
.支持库 internet

.子程序 _启动窗口_创建完毕

' 连接参数
.局部变量 服务器IP, 文本型, , "127.0.0.1"
.局部变量 端口, 整数型, , 12345
.局部变量 VMware进程ID, 整数型, , 9856

' 连接到服务器
.如果真 (连接服务器 (服务器IP, 端口, VMware进程ID))
    调试输出 ("✓ 连接成功")

    ' 获取进程ID
    .局部变量 记事本PID, 整数型
    记事本PID ＝ 获取进程ID ("notepad.exe")
    .如果真 (记事本PID ＞ 0)
        调试输出 ("✓ 找到记事本进程，PID: " ＋ 到文本 (记事本PID))

        ' 获取模块基址
        .局部变量 NTDLL基址, 长整数型
        NTDLL基址 ＝ 获取模块基址 (记事本PID, "ntdll.dll")
        .如果真 (NTDLL基址 ＞ 0)
            调试输出 ("✓ ntdll.dll基址: 0x" ＋ 到十六进制 (NTDLL基址))

            ' 读取内存
            .局部变量 内存数据, 字节集
            内存数据 ＝ 读取内存 (记事本PID, NTDLL基址, 64)
            .如果真 (取字节集长度 (内存数据) ＞ 0)
                调试输出 ("✓ 读取了 " ＋ 到文本 (取字节集长度 (内存数据)) ＋ " 字节")
            .如果结束
        .如果结束
    .如果结束

    断开连接 ()
.如果结束
```

## 🔧 API参考

### 内存操作
- `CMD_READ_MEMORY`: 读取进程虚拟内存
- `CMD_WRITE_MEMORY`: 写入进程虚拟内存
- `CMD_READ_PHYSICAL`: 读取物理内存
- `CMD_WRITE_PHYSICAL`: 写入物理内存

### 进程管理
- `CMD_GET_PROCESS_LIST`: 获取进程列表
- `CMD_GET_PROCESS_BY_NAME`: 根据名称查找进程
- `CMD_GET_PROCESS_INFO`: 获取进程详细信息

### 模块管理
- `CMD_GET_MODULE_BASE`: 获取模块基址
- `CMD_GET_MODULE_LIST`: 获取模块列表
- `CMD_GET_MODULE_INFO`: 获取模块详细信息

## 🛠️ 故障排除

### 常见问题

1. **服务器启动失败**
   ```
   解决方案:
   - 确保以管理员权限运行
   - 检查VMware是否运行虚拟机
   - 验证MemProcFS库是否正确安装
   ```

2. **POCO编译错误**
   ```
   错误: 析构函数异常规范不兼容
   解决方案:
   - 确保使用C++17或更高版本
   - 检查POCO版本 (推荐1.11.0+)
   - 使用正确的vcpkg工具链
   ```

3. **未定义标识符错误**
   ```
   错误: 未定义标识符 "g_hVMM" 或 "g_vmmMutex"
   解决方案:
   - 这些是旧版本的全局变量，已在v2.0中移除
   - 请使用最新版本的代码
   - 每个客户端现在有独立的VMM实例
   ```

3. **TCP连接失败**
   ```
   解决方案:
   - 确认服务器正在运行
   - 检查防火墙设置
   - 验证端口号是否正确 (默认12345)
   - 确认服务器IP地址正确
   ```

4. **内存读取失败**
   ```
   解决方案:
   - 确认进程ID正确
   - 检查内存地址有效性
   - 验证进程仍在运行
   ```

### 调试技巧

1. **启用详细日志**:
   ```bash
   memprocfs_server.exe --verbose
   ```

2. **测试TCP连接**:
   ```bash
   # 测试TCP连接
   telnet 127.0.0.1 12345

   # 或使用netstat检查端口
   netstat -an | findstr 12345
   ```

3. **检查进程**:
   ```bash
   tasklist | findstr memprocfs_server
   ```

## 📁 项目结构

```
MemProcFS_TCP_Server_v2.0_Native/
├── memprocfs_server_native.cpp # 原生Windows TCP服务器 (多VMware进程支持)
├── client_c.c                  # C客户端示例 (简化API)
├── client_python_simple.py     # Python客户端示例 (简化API)
├── client_易语言.e             # 易语言客户端示例
├── debug_client.c              # 调试客户端 (详细日志)
├── protocol.h                  # 协议定义 (支持多VMware进程)
├── CMakeLists_native.txt       # 原生CMake构建配置
├── build_native.bat            # 原生构建脚本
├── test_native.bat             # 原生编译测试脚本
├── test_compile_fix.c          # 编译修复测试
├── quick_test.py               # 多客户端测试脚本
├── API_Reference.md            # 详细API参考文档
└── README.md                   # 项目文档
```

## 🔒 安全注意事项

- 仅在受控环境中使用
- 确保网络连接安全
- 定期更新MemProcFS库
- 遵守相关法律法规

## 📄 许可证

本项目遵循MIT许可证，与MemProcFS保持兼容。

## 🤝 贡献

欢迎提交问题报告和功能请求！

---

**注意**: 此工具仅用于合法的安全研究和调试目的。使用者需要确保遵守所有适用的法律法规。
