@echo off
setlocal enabledelayedexpansion

echo MemProcFS POCO TCP Server Build Script
echo =====================================

:: Check for Visual Studio
where cl >nul 2>&1
if errorlevel 1 (
    echo Error: Visual Studio compiler not found
    echo Please run this script from Visual Studio Developer Command Prompt
    exit /b 1
)

:: Check for CMake
where cmake >nul 2>&1
if errorlevel 1 (
    echo Error: CMake not found
    echo Please install CMake and add it to PATH
    exit /b 1
)

:: Default values
set BUILD_TYPE=Release
set MEMPROCFS_ROOT=C:\MemProcFS
set VCPKG_ROOT=C:\vcpkg
set CLEAN=0

:: Parse command line arguments
:parse_args
if "%~1"=="" goto :done_parsing
if /i "%~1"=="debug" set BUILD_TYPE=Debug
if /i "%~1"=="release" set BUILD_TYPE=Release
if /i "%~1"=="clean" set CLEAN=1
if /i "%~1"=="--memprocfs" (
    shift
    set MEMPROCFS_ROOT=%~2
)
if /i "%~1"=="--vcpkg" (
    shift
    set VCPKG_ROOT=%~2
)
shift
goto :parse_args
:done_parsing

echo Build Configuration:
echo   Build Type: %BUILD_TYPE%
echo   MemProcFS Root: %MEMPROCFS_ROOT%
echo   vcpkg Root: %VCPKG_ROOT%
echo.

:: Check if MemProcFS exists
if not exist "%MEMPROCFS_ROOT%\vmm.dll" (
    echo Error: MemProcFS not found at %MEMPROCFS_ROOT%
    echo Please download MemProcFS and extract it to the specified path
    exit /b 1
)

:: Check if vcpkg toolchain exists
if not exist "%VCPKG_ROOT%\scripts\buildsystems\vcpkg.cmake" (
    echo Error: vcpkg toolchain not found at %VCPKG_ROOT%
    echo Please install vcpkg and POCO C++ libraries
    echo   vcpkg install poco[net,util]:x64-windows
    exit /b 1
)

:: Clean if requested
if %CLEAN%==1 (
    echo Cleaning build directory...
    if exist build rmdir /s /q build
)

:: Create build directory
if not exist build mkdir build
cd build

:: Configure with CMake
echo Configuring with CMake...
cmake .. -A x64 ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DMEMPROCFS_ROOT="%MEMPROCFS_ROOT%" ^
    -DCMAKE_TOOLCHAIN_FILE="%VCPKG_ROOT%\scripts\buildsystems\vcpkg.cmake"

if errorlevel 1 (
    echo Error: CMake configuration failed
    exit /b 1
)

:: Build
echo Building %BUILD_TYPE% configuration...
cmake --build . --config %BUILD_TYPE%

if errorlevel 1 (
    echo Error: Build failed
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Executables are in: build\bin\%BUILD_TYPE%\
echo   - memprocfs_server.exe (POCO TCP Server v2.0)
echo   - client_c.exe (C Client with simplified API)
echo   - client_python_simple.py (Python Client with simplified API)
echo   - client_易语言.e (易语言 Client example)
echo   - quick_test.py (Multi-client test script)
echo.
echo To run the server:
echo   cd build\bin\%BUILD_TYPE%
echo   memprocfs_server.exe [port]
echo.
echo To test multiple VMware processes:
echo   python quick_test.py
echo.
echo New features in v2.0:
echo   - Each client connects to specific VMware process ID
echo   - No thread locks needed (independent VMM per client)
echo   - Simplified client APIs
echo   - Real-time server operation logging
echo   - 易语言 client support
echo.

cd ..
