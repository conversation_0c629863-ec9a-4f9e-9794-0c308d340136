cmake_minimum_required(VERSION 3.16)
project(MemProcFS_TCP_Server)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Windows 10 only
if(NOT WIN32)
    message(FATAL_ERROR "This project is designed for Windows 10 only")
endif()

# Find POCO libraries
find_package(Poco REQUIRED COMPONENTS Foundation Net Util)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Compiler-specific options for MSVC
if(MSVC)
    # Enable multi-processor compilation
    add_compile_options(/MP)

    # Set warning level
    add_compile_options(/W3)

    # Define WIN32_LEAN_AND_MEAN
    add_definitions(-DWIN32_LEAN_AND_MEAN)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)

    # Set runtime library
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
endif()

# Debug build options
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG)
    if(MSVC)
        add_compile_options(/Od /Zi)
    endif()
else()
    add_definitions(-DNDEBUG)
    if(MSVC)
        add_compile_options(/O2)
    endif()
endif()

# Find MemProcFS library
# You need to set MEMPROCFS_ROOT to the MemProcFS installation directory
set(MEMPROCFS_ROOT "" CACHE PATH "Path to MemProcFS installation")

if(MEMPROCFS_ROOT)
    set(MEMPROCFS_INCLUDE_DIR "${MEMPROCFS_ROOT}/includes")
    set(MEMPROCFS_LIB_DIR "${MEMPROCFS_ROOT}/lib")
    
    if(CMAKE_SIZEOF_VOID_P EQUAL 8)
        set(MEMPROCFS_LIBRARY "${MEMPROCFS_LIB_DIR}/vmm.lib")
        set(MEMPROCFS_DLL "${MEMPROCFS_ROOT}/vmm.dll")
    else()
        message(FATAL_ERROR "MemProcFS requires 64-bit compilation")
    endif()
else()
    # Try to find in common locations
    find_path(MEMPROCFS_INCLUDE_DIR
        NAMES vmmdll.h
        PATHS
            "C:/Program Files/MemProcFS/includes"
            "C:/MemProcFS/includes"
            "${CMAKE_CURRENT_SOURCE_DIR}/MemProcFS/includes"
    )
    
    find_library(MEMPROCFS_LIBRARY
        NAMES vmm
        PATHS
            "C:/Program Files/MemProcFS/lib"
            "C:/MemProcFS/lib"
            "${CMAKE_CURRENT_SOURCE_DIR}/MemProcFS/lib"
    )
    
    find_file(MEMPROCFS_DLL
        NAMES vmm.dll
        PATHS
            "C:/Program Files/MemProcFS"
            "C:/MemProcFS"
            "${CMAKE_CURRENT_SOURCE_DIR}/MemProcFS"
    )
endif()

# Check if MemProcFS was found
if(NOT MEMPROCFS_INCLUDE_DIR OR NOT MEMPROCFS_LIBRARY)
    message(FATAL_ERROR "MemProcFS not found. Please set MEMPROCFS_ROOT or install MemProcFS in a standard location.")
endif()

message(STATUS "MemProcFS include directory: ${MEMPROCFS_INCLUDE_DIR}")
message(STATUS "MemProcFS library: ${MEMPROCFS_LIBRARY}")
if(MEMPROCFS_DLL)
    message(STATUS "MemProcFS DLL: ${MEMPROCFS_DLL}")
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${MEMPROCFS_INCLUDE_DIR})

# Source files
set(SERVER_SOURCES
    memprocfs_server.cpp
    protocol.h
)

set(CLIENT_C_SOURCES
    client_c.c
    protocol.h
)

# Create the server executable
add_executable(memprocfs_server ${SERVER_SOURCES})

# Link libraries for server
target_link_libraries(memprocfs_server
    ${MEMPROCFS_LIBRARY}
    Poco::Foundation
    Poco::Net
    Poco::Util
    kernel32
    user32
    advapi32
)

# Create the C client executable
add_executable(client_c ${CLIENT_C_SOURCES})

# Link libraries for C client
target_link_libraries(client_c
    kernel32
    user32
)

# Set target properties
set_target_properties(memprocfs_server PROPERTIES
    OUTPUT_NAME "memprocfs_server"
    DEBUG_POSTFIX "_d"
)

set_target_properties(client_c PROPERTIES
    OUTPUT_NAME "client_c"
    DEBUG_POSTFIX "_d"
)

# Copy MemProcFS DLL to output directory
if(MEMPROCFS_DLL)
    add_custom_command(TARGET memprocfs_server POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${MEMPROCFS_DLL}"
        $<TARGET_FILE_DIR:memprocfs_server>
        COMMENT "Copying MemProcFS DLL to output directory"
    )
endif()

# Copy Python and 易语言 clients to output directory
add_custom_command(TARGET memprocfs_server POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/client_python_simple.py"
    $<TARGET_FILE_DIR:memprocfs_server>
    COMMENT "Copying Python client to output directory"
)

add_custom_command(TARGET memprocfs_server POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/client_易语言.e"
    $<TARGET_FILE_DIR:memprocfs_server>
    COMMENT "Copying 易语言 client to output directory"
)

# Installation
install(TARGETS memprocfs_server client_c
    RUNTIME DESTINATION bin
)

install(FILES
    protocol.h
    client_python_simple.py
    client_易语言.e
    DESTINATION bin
)

if(MEMPROCFS_DLL)
    install(FILES ${MEMPROCFS_DLL}
        DESTINATION bin
    )
endif()

# Create a simple test
enable_testing()

# Test that server can start (will fail if VMware not running, but that's expected)
add_test(NAME server_help_test 
    COMMAND memprocfs_server --help
    WORKING_DIRECTORY ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}
)
set_tests_properties(server_help_test PROPERTIES
    WILL_FAIL TRUE  # Expected to fail since --help is not implemented
)

# Print configuration summary
message(STATUS "")
message(STATUS "Configuration Summary:")
message(STATUS "  Project: ${PROJECT_NAME}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Generator: ${CMAKE_GENERATOR}")
message(STATUS "  Architecture: ${CMAKE_SIZEOF_VOID_P EQUAL 8 ? "64-bit" : "32-bit"}")
message(STATUS "  Install Prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")
message(STATUS "Targets:")
message(STATUS "  memprocfs_server - 64-bit TCP server")
message(STATUS "  client_c         - C client example")
message(STATUS "")
message(STATUS "Additional files:")
message(STATUS "  client_python_simple.py - Python client example")
message(STATUS "  client_易语言.e - 易语言 client example")
message(STATUS "")
