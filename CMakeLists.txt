cmake_minimum_required(VERSION 3.16)
project(MemProcFS_TCP_Server VERSION 2.0.0)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Windows only
if(NOT WIN32)
    message(FATAL_ERROR "This project only supports Windows")
endif()

# 查找MemProcFS
if(NOT DEFINED MEMPROCFS_ROOT)
    message(FATAL_ERROR "Please set MEMPROCFS_ROOT to the MemProcFS installation directory")
endif()

# 验证MemProcFS文件
if(NOT EXISTS "${MEMPROCFS_ROOT}/vmmdll.h")
    message(FATAL_ERROR "vmmdll.h not found in ${MEMPROCFS_ROOT}")
endif()

if(NOT EXISTS "${MEMPROCFS_ROOT}/vmmdll.lib")
    message(FATAL_ERROR "vmmdll.lib not found in ${MEMPROCFS_ROOT}")
endif()

if(NOT EXISTS "${MEMPROCFS_ROOT}/vmmdll.dll")
    message(FATAL_ERROR "vmmdll.dll not found in ${MEMPROCFS_ROOT}")
endif()

# 包含目录
include_directories(${MEMPROCFS_ROOT})

# 链接库
link_directories(${MEMPROCFS_ROOT})

# 创建服务器可执行文件
add_executable(memprocfs_server
    memprocfs_server_native.cpp
    protocol.h
)

# 链接库
target_link_libraries(memprocfs_server
    vmmdll
    ws2_32
)

# 创建C客户端可执行文件
add_executable(client_c
    client_c.c
    protocol.h
)

target_link_libraries(client_c
    ws2_32
)

# 创建调试客户端可执行文件
add_executable(debug_client
    debug_client.c
    protocol.h
)

target_link_libraries(debug_client
    ws2_32
)

# 创建测试客户端可执行文件
add_executable(test_simple
    test_simple.c
    protocol.h
)

target_link_libraries(test_simple
    ws2_32
)

# 设置输出目录
set_target_properties(memprocfs_server client_c debug_client test_simple
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}/bin/Debug"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}/bin/Release"
)

# 复制MemProcFS DLL到输出目录
add_custom_command(TARGET memprocfs_server POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${MEMPROCFS_ROOT}/vmmdll.dll"
    $<TARGET_FILE_DIR:memprocfs_server>
    COMMENT "Copying MemProcFS DLL to output directory"
)

# 复制客户端文件到输出目录
add_custom_command(TARGET memprocfs_server POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/client_python_simple.py"
    $<TARGET_FILE_DIR:memprocfs_server>
    COMMENT "Copying Python client to output directory"
)

add_custom_command(TARGET memprocfs_server POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/client_易语言.e"
    $<TARGET_FILE_DIR:memprocfs_server>
    COMMENT "Copying 易语言 client to output directory"
)

add_custom_command(TARGET memprocfs_server POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/quick_test.py"
    $<TARGET_FILE_DIR:memprocfs_server>
    COMMENT "Copying test script to output directory"
)

add_custom_command(TARGET memprocfs_server POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_CURRENT_SOURCE_DIR}/API_Reference.md"
    $<TARGET_FILE_DIR:memprocfs_server>
    COMMENT "Copying API reference to output directory"
)

# 安装目标
install(TARGETS memprocfs_server client_c debug_client test_simple
    RUNTIME DESTINATION bin
)

install(FILES 
    protocol.h
    client_python_simple.py
    client_易语言.e
    quick_test.py
    API_Reference.md
    DESTINATION bin
)

install(FILES "${MEMPROCFS_ROOT}/vmmdll.dll"
    DESTINATION bin
)

# 显示构建信息
message(STATUS "")
message(STATUS "MemProcFS TCP Server v2.0 - Windows Build")
message(STATUS "==========================================")
message(STATUS "")
message(STATUS "Configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  MemProcFS root: ${MEMPROCFS_ROOT}")
message(STATUS "  Output directory: ${CMAKE_BINARY_DIR}/bin")
message(STATUS "")
message(STATUS "Targets:")
message(STATUS "  memprocfs_server - Native Windows TCP server")
message(STATUS "  client_c - C client with simplified API")
message(STATUS "  debug_client - Debug client with detailed logging")
message(STATUS "  test_simple - Single packet transmission test")
message(STATUS "")
message(STATUS "Additional files:")
message(STATUS "  client_python_simple.py - Python client example")
message(STATUS "  client_易语言.e - 易语言 client example")
message(STATUS "  quick_test.py - Multi-client test script")
message(STATUS "  API_Reference.md - Complete API documentation")
message(STATUS "")
