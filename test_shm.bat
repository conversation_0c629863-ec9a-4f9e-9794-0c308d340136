@echo off
echo MemProcFS Shared Memory Server - Build Test
echo =========================================

:: 测试协议头文件编译
echo Testing protocol header compilation...
cl /EHsc test_compile.cpp /Fe:test_compile.exe >nul 2>&1
if errorlevel 1 (
    echo Error: Protocol header compilation failed
    echo Running detailed compilation test...
    cl /EHsc test_compile.cpp /Fe:test_compile.exe
    exit /b 1
) else (
    echo ✓ Protocol header compilation: OK
    echo Running structure size test...
    test_compile.exe
    del test_compile.exe >nul 2>&1
)

:: 测试C客户端编译
echo Testing C client compilation...
cl client_shm.c /Fe:test_client_shm.exe >nul 2>&1
if errorlevel 1 (
    echo Error: C client compilation failed
    exit /b 1
) else (
    echo ✓ C client compilation: OK
    del test_client_shm.exe >nul 2>&1
)

:: 测试Python客户端语法
echo Testing Python client syntax...
python -m py_compile client_shm.py >nul 2>&1
if errorlevel 1 (
    echo Error: Python client syntax error
    exit /b 1
) else (
    echo ✓ Python client syntax: OK
    del __pycache__ /s /q >nul 2>&1
    rmdir __pycache__ >nul 2>&1
)

:: 清理临时文件
del *.obj >nul 2>&1

echo.
echo All tests passed!
echo.
echo Ready to build with:
echo   build_shm.bat "C:\path\to\MemProcFS"
