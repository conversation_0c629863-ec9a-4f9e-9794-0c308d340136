@echo off
echo MemProcFS Shared Memory Server - Build Test
echo =========================================

:: 测试协议头文件编译
echo Testing protocol header compilation...
echo #include "shared_memory_protocol.h" > test_protocol.cpp
echo int main() { return 0; } >> test_protocol.cpp

cl /EHsc test_protocol.cpp /Fe:test_protocol.exe >nul 2>&1
if errorlevel 1 (
    echo Error: Protocol header compilation failed
    del test_protocol.cpp >nul 2>&1
    exit /b 1
) else (
    echo ✓ Protocol header compilation: OK
    del test_protocol.cpp test_protocol.exe >nul 2>&1
)

:: 测试C客户端编译
echo Testing C client compilation...
cl client_shm.c /Fe:test_client_shm.exe >nul 2>&1
if errorlevel 1 (
    echo Error: C client compilation failed
    exit /b 1
) else (
    echo ✓ C client compilation: OK
    del test_client_shm.exe >nul 2>&1
)

:: 测试Python客户端语法
echo Testing Python client syntax...
python -m py_compile client_shm.py >nul 2>&1
if errorlevel 1 (
    echo Error: Python client syntax error
    exit /b 1
) else (
    echo ✓ Python client syntax: OK
    del __pycache__ /s /q >nul 2>&1
    rmdir __pycache__ >nul 2>&1
)

:: 清理临时文件
del *.obj >nul 2>&1

echo.
echo All tests passed!
echo.
echo Ready to build with:
echo   build_shm.bat "C:\path\to\MemProcFS"
