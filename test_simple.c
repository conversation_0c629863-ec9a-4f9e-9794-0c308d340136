/*
 * 简单的单包发送测试程序
 * 允许用户指定VMware进程ID
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define closesocket close
#endif

#include "protocol.h"

// 发送数据
int send_data(SOCKET socket, const void* data, uint32_t size) {
    const uint8_t* bytes = (const uint8_t*)data;
    uint32_t sent = 0;
    
    while (sent < size) {
        int result = send(socket, (const char*)(bytes + sent), size - sent, 0);
        if (result <= 0) {
            return 0;
        }
        sent += result;
    }
    return 1;
}

// 接收数据
int receive_data(SOCKET socket, void* data, uint32_t size) {
    uint8_t* bytes = (uint8_t*)data;
    uint32_t received = 0;
    
    while (received < size) {
        int result = recv(socket, (char*)(bytes + received), size - received, 0);
        if (result <= 0) {
            return 0;
        }
        received += result;
    }
    return 1;
}

// 获取状态码描述
const char* get_status_string(uint8_t status) {
    switch(status) {
        case STATUS_SUCCESS: return "SUCCESS";
        case STATUS_ERROR_INVALID_CMD: return "INVALID_CMD";
        case STATUS_ERROR_INVALID_PARAM: return "INVALID_PARAM";
        case STATUS_ERROR_READ_FAILED: return "READ_FAILED";
        case STATUS_ERROR_WRITE_FAILED: return "WRITE_FAILED";
        case STATUS_ERROR_PROCESS_NOT_FOUND: return "PROCESS_NOT_FOUND";
        case STATUS_ERROR_MODULE_NOT_FOUND: return "MODULE_NOT_FOUND";
        case STATUS_ERROR_MEMORY_ACCESS: return "MEMORY_ACCESS";
        case STATUS_ERROR_INTERNAL: return "INTERNAL";
        case STATUS_ERROR_NOT_INITIALIZED: return "NOT_INITIALIZED";
        case STATUS_ERROR_BUFFER_TOO_SMALL: return "BUFFER_TOO_SMALL";
        default: return "UNKNOWN";
    }
}

// 测试单包发送
int test_single_packet(SOCKET socket, uint32_t vmware_pid) {
    printf("\n=== 测试单包发送功能 ===\n");
    printf("VMware进程ID: %u\n", vmware_pid);
    
    // 发送初始化命令
    packet_header_t header;
    header.magic = PACKET_MAGIC;
    header.version = MEMPROCFS_PROTOCOL_VERSION;
    header.command = CMD_INIT;
    header.flags = 0;
    header.data_size = sizeof(uint32_t);
    header.sequence = 1;
    
    printf("\n1. 发送初始化命令...\n");
    printf("   数据包头: Magic=0x%08X, Cmd=0x%02X, DataSize=%u, Seq=%u\n",
           header.magic, header.command, header.data_size, header.sequence);
    
    if (!send_data(socket, &header, sizeof(header))) {
        printf("✗ 发送数据包头失败\n");
        return 0;
    }
    
    if (!send_data(socket, &vmware_pid, sizeof(vmware_pid))) {
        printf("✗ 发送VMware进程ID失败\n");
        return 0;
    }
    
    printf("✓ 发送完成\n");
    
    // 接收响应
    printf("\n2. 接收响应...\n");
    
    packet_header_t response_header;
    if (!receive_data(socket, &response_header, sizeof(response_header))) {
        printf("✗ 接收响应包头失败\n");
        return 0;
    }
    
    printf("   响应包头: Magic=0x%08X, Cmd=0x%02X, DataSize=%u, Seq=%u\n",
           response_header.magic, response_header.command, 
           response_header.data_size, response_header.sequence);
    
    // 验证响应包头
    if (response_header.magic != PACKET_MAGIC) {
        printf("✗ 响应包头Magic错误: 0x%08X (期望: 0x%08X)\n", 
               response_header.magic, PACKET_MAGIC);
        return 0;
    }
    
    if (response_header.command != CMD_RESPONSE) {
        printf("✗ 响应包头Command错误: 0x%02X (期望: 0x%02X)\n", 
               response_header.command, CMD_RESPONSE);
        return 0;
    }
    
    if (response_header.data_size == 0) {
        printf("✗ 响应数据大小为0\n");
        return 0;
    }
    
    // 接收响应数据
    uint8_t* response_data = malloc(response_header.data_size);
    if (!response_data) {
        printf("✗ 内存分配失败\n");
        return 0;
    }
    
    if (!receive_data(socket, response_data, response_header.data_size)) {
        printf("✗ 接收响应数据失败\n");
        free(response_data);
        return 0;
    }
    
    printf("✓ 接收完成\n");
    
    // 解析响应数据
    printf("\n3. 解析响应数据...\n");
    
    if (response_header.data_size < sizeof(response_header_t)) {
        printf("✗ 响应数据太小: %u bytes (期望至少: %zu bytes)\n", 
               response_header.data_size, sizeof(response_header_t));
        free(response_data);
        return 0;
    }
    
    response_header_t* resp_hdr = (response_header_t*)response_data;
    const char* status_str = get_status_string(resp_hdr->status);
    
    printf("   响应状态: %u (%s)\n", resp_hdr->status, status_str);
    printf("   数据大小: %u bytes\n", resp_hdr->data_size);
    
    free(response_data);
    
    // 判断测试结果
    printf("\n4. 测试结果:\n");
    printf("✓ 服务器使用单包发送 (数据包头 + 响应头 + 数据)\n");
    
    if (resp_hdr->status == STATUS_SUCCESS) {
        printf("✓ VMware进程初始化成功\n");
        printf("✓ 测试完全成功！\n");
        return 1;
    } else {
        printf("! VMware进程初始化失败: %s\n", status_str);
        printf("✓ 但单包发送功能正常工作\n");
        return 1;  // 单包发送测试成功
    }
}

int main(int argc, char* argv[]) {
    printf("MemProcFS TCP服务器 - 简单测试程序\n");
    printf("==================================\n");
    
    uint32_t vmware_pid = 12345;  // 默认进程ID
    
    // 检查命令行参数
    if (argc > 1) {
        vmware_pid = (uint32_t)atoi(argv[1]);
        printf("使用命令行指定的VMware进程ID: %u\n", vmware_pid);
    } else {
        printf("使用默认VMware进程ID: %u\n", vmware_pid);
        printf("您可以通过命令行参数指定进程ID: %s <进程ID>\n", argv[0]);
    }
    
#ifdef _WIN32
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        printf("✗ WSAStartup失败\n");
        return 1;
    }
#endif

    SOCKET client_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (client_socket == INVALID_SOCKET) {
        printf("✗ 创建socket失败\n");
#ifdef _WIN32
        WSACleanup();
#endif
        return 1;
    }

    struct sockaddr_in server_addr;
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(12345);
    inet_pton(AF_INET, "127.0.0.1", &server_addr.sin_addr);

    printf("\n连接到服务器 127.0.0.1:12345...\n");
    if (connect(client_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        printf("✗ 连接失败\n");
        printf("请确保MemProcFS服务器正在运行\n");
        closesocket(client_socket);
#ifdef _WIN32
        WSACleanup();
#endif
        return 1;
    }

    printf("✓ 连接成功\n");
    
    // 执行测试
    int result = test_single_packet(client_socket, vmware_pid);
    
    closesocket(client_socket);
#ifdef _WIN32
    WSACleanup();
#endif
    
    printf("\n=== 总结 ===\n");
    if (result) {
        printf("✓ 单包发送测试成功！\n");
        printf("服务器正确地将响应头和数据合并成一个包发送。\n");
    } else {
        printf("✗ 单包发送测试失败！\n");
    }
    
    return result ? 0 : 1;
}
