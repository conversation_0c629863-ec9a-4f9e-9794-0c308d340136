/*
 * 编译测试程序
 * 验证所有头文件和结构体定义是否正确
 */

#include <iostream>
#include <windows.h>
#include "shared_memory_protocol.h"

int main() {
    std::cout << "Testing shared memory protocol compilation..." << std::endl;
    
    // 测试结构体大小
    std::cout << "Structure sizes:" << std::endl;
    std::cout << "  request_header_t: " << sizeof(request_header_t) << " bytes" << std::endl;
    std::cout << "  response_header_t: " << sizeof(response_header_t) << " bytes" << std::endl;
    std::cout << "  init_request_t: " << sizeof(init_request_t) << " bytes" << std::endl;
    std::cout << "  get_process_id_request_t: " << sizeof(get_process_id_request_t) << " bytes" << std::endl;
    std::cout << "  get_process_id_response_t: " << sizeof(get_process_id_response_t) << " bytes" << std::endl;
    std::cout << "  memory_request_t: " << sizeof(memory_request_t) << " bytes" << std::endl;
    std::cout << "  memory_response_t: " << sizeof(memory_response_t) << " bytes" << std::endl;
    std::cout << "  shared_memory_t: " << sizeof(shared_memory_t) << " bytes" << std::endl;
    
    // 测试常量
    std::cout << "\nConstants:" << std::endl;
    std::cout << "  PACKET_MAGIC: 0x" << std::hex << PACKET_MAGIC << std::dec << std::endl;
    std::cout << "  MEMPROCFS_PROTOCOL_VERSION: " << MEMPROCFS_PROTOCOL_VERSION << std::endl;
    std::cout << "  MEMPROCFS_SHM_SIZE: " << MEMPROCFS_SHM_SIZE << " bytes" << std::endl;
    
    // 测试辅助函数
    request_header_t req_header;
    init_request_header(&req_header, CMD_INIT, 4, 1);
    
    response_header_t resp_header;
    init_response_header(&resp_header, STATUS_SUCCESS, 0);
    
    std::cout << "\nHelper functions test:" << std::endl;
    std::cout << "  Request header magic: 0x" << std::hex << req_header.magic << std::dec << std::endl;
    std::cout << "  Response header magic: 0x" << std::hex << resp_header.magic << std::dec << std::endl;
    
    std::cout << "\nCompilation test passed!" << std::endl;
    return 0;
}
