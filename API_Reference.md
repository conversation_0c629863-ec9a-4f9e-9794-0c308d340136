# MemProcFS TCP Server v2.0 - API参考

## 🔧 C客户端简化API

### 连接管理

```c
// 初始化客户端并连接到指定VMware进程
int memprocfs_client_init(memprocfs_client_t* client, const char* server_ip, uint16_t port, uint32_t vmware_process_id);

// 清理客户端连接
void memprocfs_client_cleanup(memprocfs_client_t* client);
```

### 内存操作

```c
// 读取内存 - 返回实际读取的字节数
int memprocfs_read_memory_simple(memprocfs_client_t* client, uint32_t process_id, uint64_t address, void* buffer, uint32_t size);

// 写入内存 - 返回实际写入的字节数
int memprocfs_write_memory_simple(memprocfs_client_t* client, uint32_t process_id, uint64_t address, const void* buffer, uint32_t size);
```

### 进程和模块操作

```c
// 根据进程名获取进程ID - 返回PID，失败返回0
uint32_t memprocfs_get_process_id(memprocfs_client_t* client, const char* process_name);

// 获取模块基址 - 返回基址，失败返回0
uint64_t memprocfs_get_module_base(memprocfs_client_t* client, uint32_t process_id, const char* module_name);
```

## 🐍 Python客户端简化API

### 连接管理

```python
# 初始化客户端
client = MemProcFSClient(server_ip="127.0.0.1", port=12345, vmware_process_id=9856)

# 连接到服务器
client.connect()

# 断开连接
client.disconnect()
```

### 内存操作

```python
# 读取内存 - 返回字节数据或None
data = client.read_memory_simple(process_id, address, size)

# 写入内存 - 返回写入的字节数
bytes_written = client.write_memory_simple(process_id, address, data)
```

### 进程和模块操作

```python
# 获取进程ID - 返回PID或0
pid = client.get_process_id("notepad.exe")

# 获取模块基址 - 返回基址或0
base_addr = client.get_module_base_simple(process_id, "ntdll.dll")
```

## 📋 易语言客户端API

### 连接管理

```
连接服务器(IP地址, 端口, VMware进程ID) -> 逻辑型
断开连接()
```

### 内存操作

```
读取内存(进程ID, 地址, 大小) -> 字节集
写入内存(进程ID, 地址, 数据) -> 整数型
```

### 进程和模块操作

```
获取进程ID(进程名) -> 整数型
获取模块基址(进程ID, 模块名) -> 长整数型
```

## 🔄 使用流程

1. **获取VMware进程ID**
   ```bash
   tasklist | findstr vmware
   ```

2. **初始化客户端**
   ```c
   // C语言
   memprocfs_client_init(&client, "127.0.0.1", 12345, vmware_pid);
   
   // Python
   client = MemProcFSClient(vmware_process_id=vmware_pid)
   client.connect()
   ```

3. **执行操作**
   ```c
   // 获取进程ID
   uint32_t pid = memprocfs_get_process_id(&client, "notepad.exe");
   
   // 获取模块基址
   uint64_t base = memprocfs_get_module_base(&client, pid, "ntdll.dll");
   
   // 读取内存
   uint8_t buffer[64];
   int bytes_read = memprocfs_read_memory_simple(&client, pid, base, buffer, 64);
   ```

4. **清理资源**
   ```c
   memprocfs_client_cleanup(&client);
   ```

## ⚠️ 注意事项

- 每个客户端必须指定VMware进程ID
- 服务器会为每个客户端创建独立的VMM实例
- 所有操作都会在服务器控制台显示详细日志
- 错误时函数返回0或NULL，成功时返回有效值

## 🔧 故障排除

### 常见错误

1. **"Response buffer too small"**
   ```
   原因: 客户端响应缓冲区太小
   解决: 确保缓冲区大小 = sizeof(response_header_t) + sizeof(具体响应结构)
   ```

2. **"Connection reset by peer"**
   ```
   原因: 客户端发送了错误的数据包或缓冲区大小不匹配
   解决: 检查数据包格式和缓冲区大小
   ```

3. **进程或模块未找到**
   ```
   原因: 目标进程不在指定的VMware虚拟机中运行
   解决: 确保进程在正确的虚拟机中运行
   ```

### 调试工具

- `debug_client.c` - 显示详细的通信过程
- `test_buffer_fix.c` - 验证缓冲区大小计算
- `quick_test.py` - 多客户端并发测试

### 缓冲区大小参考

```c
// 获取进程ID响应
uint8_t buffer[sizeof(response_header_t) + sizeof(process_by_name_response_t)];

// 获取模块基址响应
uint8_t buffer[sizeof(response_header_t) + sizeof(module_base_response_t)];

// 内存操作响应
uint8_t buffer[sizeof(response_header_t) + sizeof(memory_response_t) + 数据大小];
```
